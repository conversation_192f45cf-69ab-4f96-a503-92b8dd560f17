{"name": "frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "build:analyze": "tsc -b && vite build --mode analyze", "preview": "vite preview", "lint": "eslint src --ext ts,tsx", "lint:fix": "eslint src --ext ts,tsx --fix", "typecheck": "tsc --noEmit"}, "dependencies": {"@ant-design/icons": "^6.0.0", "antd": "^5.24.8", "axios": "^1.9.0", "echarts": "^5.6.0", "mermaid": "^11.6.0", "react": "^19.0.0", "react-dom": "^19.0.0", "react-markdown": "^10.1.0", "react-syntax-highlighter": "^15.6.1", "rehype-raw": "^7.0.0", "rehype-sanitize": "^6.0.0", "remark-gfm": "^4.0.1", "socket.io-client": "^4.8.1", "zustand": "^5.0.4"}, "devDependencies": {"@eslint/js": "^9.22.0", "@types/lodash": "^4.17.16", "@types/node": "^22.15.3", "@types/react": "^19.0.10", "@types/react-dom": "^19.0.4", "@types/react-syntax-highlighter": "^15.5.13", "@vitejs/plugin-react": "^4.3.4", "eslint": "^9.22.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^16.0.0", "typescript": "~5.7.2", "typescript-eslint": "^8.26.1", "vite": "^6.3.1"}}