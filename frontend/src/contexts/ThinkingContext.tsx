import React, { createContext, useContext, useState, ReactNode } from 'react';
import { ThinkingStep } from '../services/socketService';

// 定义上下文类型
interface ThinkingContextType {
  thinkingSteps: ThinkingStep[];
  setThinkingSteps: React.Dispatch<React.SetStateAction<ThinkingStep[]>>;
  expandedSteps: Record<string, boolean>;
  setExpandedSteps: React.Dispatch<React.SetStateAction<Record<string, boolean>>>;
  clearThinkingState: () => void;
}

// 创建上下文
const ThinkingContext = createContext<ThinkingContextType | undefined>(undefined);

// 上下文提供者组件
interface ThinkingProviderProps {
  children: ReactNode;
}

export const ThinkingProvider: React.FC<ThinkingProviderProps> = ({ children }) => {
  // 状态
  const [thinkingSteps, setThinkingSteps] = useState<ThinkingStep[]>([]);
  const [expandedSteps, setExpandedSteps] = useState<Record<string, boolean>>({});

  // 清除思考状态
  const clearThinkingState = () => {
    setThinkingSteps([]);
    setExpandedSteps({});
  };

  // 提供上下文值
  const contextValue: ThinkingContextType = {
    thinkingSteps,
    setThinkingSteps,
    expandedSteps,
    setExpandedSteps,
    clearThinkingState
  };

  return (
    <ThinkingContext.Provider value={contextValue}>
      {children}
    </ThinkingContext.Provider>
  );
};

// 自定义钩子，用于访问上下文
export const useThinking = (): ThinkingContextType => {
  const context = useContext(ThinkingContext);
  if (context === undefined) {
    throw new Error('useThinking must be used within a ThinkingProvider');
  }
  return context;
};
