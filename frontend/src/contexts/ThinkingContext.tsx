import React, { createContext, useContext, useState, ReactNode } from 'react';
import { ThinkingStep } from '../services/socketService';

// 深度思考会话信息
interface ThinkingSession {
  id: string;
  level: number;
  trigger: 'user' | 'auto';
  reason?: string;
  parentSessionId?: string;
  steps: ThinkingStep[];
  expandedSteps: Record<string, boolean>;
  startTime: number;
}

// 定义上下文类型
interface ThinkingContextType {
  // 兼容旧的接口
  thinkingSteps: ThinkingStep[];
  setThinkingSteps: React.Dispatch<React.SetStateAction<ThinkingStep[]>>;
  expandedSteps: Record<string, boolean>;
  setExpandedSteps: React.Dispatch<React.SetStateAction<Record<string, boolean>>>;
  clearThinkingState: () => void;

  // 新的层级管理接口
  thinkingSessions: ThinkingSession[];
  currentSession: ThinkingSession | null;
  addThinkingSession: (level: number, trigger: 'user' | 'auto', reason?: string, parentSessionId?: string) => string;
  updateSessionSteps: (sessionId: string, steps: ThinkingStep[]) => void;
  updateSessionExpandedSteps: (sessionId: string, expandedSteps: Record<string, boolean>) => void;
  endThinkingSession: (sessionId: string) => void;
  getCurrentLevel: () => number;
}

// 创建上下文
const ThinkingContext = createContext<ThinkingContextType | undefined>(undefined);

// 上下文提供者组件
interface ThinkingProviderProps {
  children: ReactNode;
}

export const ThinkingProvider: React.FC<ThinkingProviderProps> = ({ children }) => {
  // 兼容旧的状态
  const [thinkingSteps, setThinkingSteps] = useState<ThinkingStep[]>([]);
  const [expandedSteps, setExpandedSteps] = useState<Record<string, boolean>>({});

  // 新的层级管理状态
  const [thinkingSessions, setThinkingSessions] = useState<ThinkingSession[]>([]);
  const [currentSession, setCurrentSession] = useState<ThinkingSession | null>(null);

  // 清除思考状态
  const clearThinkingState = () => {
    setThinkingSteps([]);
    setExpandedSteps({});
    setThinkingSessions([]);
    setCurrentSession(null);
  };

  // 添加新的思考会话
  const addThinkingSession = (level: number, trigger: 'user' | 'auto', reason?: string, parentSessionId?: string): string => {
    const sessionId = `session-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
    const newSession: ThinkingSession = {
      id: sessionId,
      level,
      trigger,
      reason,
      parentSessionId,
      steps: [],
      expandedSteps: {},
      startTime: Date.now()
    };

    setThinkingSessions(prev => [...prev, newSession]);
    setCurrentSession(newSession);

    // 更新兼容的状态
    setThinkingSteps([]);
    setExpandedSteps({});

    return sessionId;
  };

  // 更新会话的步骤
  const updateSessionSteps = (sessionId: string, steps: ThinkingStep[]) => {
    setThinkingSessions(prev =>
      prev.map(session =>
        session.id === sessionId
          ? { ...session, steps }
          : session
      )
    );

    // 如果是当前会话，同时更新兼容的状态
    if (currentSession?.id === sessionId) {
      setThinkingSteps(steps);
    }
  };

  // 更新会话的展开状态
  const updateSessionExpandedSteps = (sessionId: string, expandedSteps: Record<string, boolean>) => {
    setThinkingSessions(prev =>
      prev.map(session =>
        session.id === sessionId
          ? { ...session, expandedSteps }
          : session
      )
    );

    // 如果是当前会话，同时更新兼容的状态
    if (currentSession?.id === sessionId) {
      setExpandedSteps(expandedSteps);
    }
  };

  // 结束思考会话
  const endThinkingSession = (sessionId: string) => {
    const session = thinkingSessions.find(s => s.id === sessionId);
    if (!session) return;

    // 如果结束的是当前会话，切换到父会话或清空
    if (currentSession?.id === sessionId) {
      if (session.parentSessionId) {
        const parentSession = thinkingSessions.find(s => s.id === session.parentSessionId);
        if (parentSession) {
          setCurrentSession(parentSession);
          setThinkingSteps(parentSession.steps);
          setExpandedSteps(parentSession.expandedSteps);
        } else {
          setCurrentSession(null);
          setThinkingSteps([]);
          setExpandedSteps({});
        }
      } else {
        setCurrentSession(null);
        setThinkingSteps([]);
        setExpandedSteps({});
      }
    }
  };

  // 获取当前深度思考层级
  const getCurrentLevel = (): number => {
    return currentSession?.level || 0;
  };

  // 提供上下文值
  const contextValue: ThinkingContextType = {
    // 兼容旧的接口
    thinkingSteps,
    setThinkingSteps,
    expandedSteps,
    setExpandedSteps,
    clearThinkingState,

    // 新的层级管理接口
    thinkingSessions,
    currentSession,
    addThinkingSession,
    updateSessionSteps,
    updateSessionExpandedSteps,
    endThinkingSession,
    getCurrentLevel
  };

  return (
    <ThinkingContext.Provider value={contextValue}>
      {children}
    </ThinkingContext.Provider>
  );
};

// 自定义钩子，用于访问上下文
export const useThinking = (): ThinkingContextType => {
  const context = useContext(ThinkingContext);
  if (context === undefined) {
    throw new Error('useThinking must be used within a ThinkingProvider');
  }
  return context;
};
