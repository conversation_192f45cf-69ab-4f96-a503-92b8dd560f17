/* 思考内容样式 */
.thinking-block {
  margin: 12px 0;
}

.thinking-content {
  background-color: #fffbe6;
  padding: 12px;
  border-radius: 4px;
  border: 1px solid #ffe58f;
  margin-top: 8px;
  font-size: 14px;
  line-height: 1.5;
  color: #595959;
}

.thinking-header {
  display: flex;
  align-items: center;
  color: #8c8c8c;
  font-style: italic;
  cursor: pointer;
  user-select: none;
}

.thinking-header:hover {
  color: #faad14;
}

.thinking-header .anticon {
  margin-right: 8px;
}

/* 代码块样式优化 */
.thinking-content pre {
  margin: 8px 0;
  border-radius: 4px;
  overflow: auto;
}

.thinking-content code {
  font-family: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, Courier, monospace;
}

.thinking-content p {
  margin: 8px 0;
}

/* 折叠面板样式 */
.thinking-block .ant-collapse-ghost > .ant-collapse-item > .ant-collapse-header {
  padding: 6px 0;
}

.thinking-block .ant-collapse-ghost > .ant-collapse-item > .ant-collapse-content > .ant-collapse-content-box {
  padding: 0;
}
