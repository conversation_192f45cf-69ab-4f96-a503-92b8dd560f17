/* 思考状态样式 */
.thinking-status {
  display: flex;
  align-items: center;
  margin: 16px 0;
  animation: fadeIn 0.3s ease-in-out;
}

.thinking-status-content {
  display: flex;
  align-items: center;
  background-color: #f0f5ff;
  border-radius: 8px;
  padding: 12px 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  border-left: 3px solid #1890ff;
}

.thinking-text {
  margin-left: 12px;
  color: #1890ff;
  font-size: 16px;
}

/* 淡入动画 */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 思考中的点动画 */
.thinking-dots::after {
  content: '';
  animation: dots 1.5s infinite;
}

@keyframes dots {
  0%, 20% { content: ''; }
  40% { content: '.'; }
  60% { content: '..'; }
  80%, 100% { content: '...'; }
}
