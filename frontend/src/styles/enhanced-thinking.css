/* 增强的深度思考样式 */

/* 层级指示器样式 */
.thinking-level-indicator {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 16px;
  padding: 8px 12px;
  background: linear-gradient(135deg, #f0f5ff 0%, #e6f7ff 100%);
  border-radius: 8px;
  border: 1px solid #d6e4ff;
}

.thinking-level-indicator.level-2 {
  background: linear-gradient(135deg, #f9f0ff 0%, #efdbff 100%);
  border-color: #d3adf7;
}

/* 第一层深度思考容器 */
.thinking-container-level-1 {
  background: #ffffff;
  border: 2px solid #1890ff;
  border-radius: 12px;
  padding: 16px;
  margin: 16px 0;
  box-shadow: 0 4px 12px rgba(24, 144, 255, 0.1);
}

/* 第二层深度思考容器 */
.thinking-container-level-2 {
  background: #fafafa;
  border: 2px solid #722ed1;
  border-radius: 12px;
  padding: 16px;
  margin: 16px 0;
  box-shadow: 0 4px 12px rgba(114, 46, 209, 0.15);
  position: relative;
}

.thinking-container-level-2::before {
  content: '';
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  background: linear-gradient(45deg, #722ed1, #9254de, #b37feb);
  border-radius: 12px;
  z-index: -1;
  animation: borderGlow 3s ease-in-out infinite alternate;
}

@keyframes borderGlow {
  0% {
    opacity: 0.5;
  }
  100% {
    opacity: 0.8;
  }
}

/* 思考步骤样式增强 */
.thinking-step-level-1 {
  background: #ffffff;
  border-left: 4px solid #1890ff;
  padding: 12px 16px;
  margin: 8px 0;
  border-radius: 0 8px 8px 0;
  transition: all 0.3s ease;
}

.thinking-step-level-1:hover {
  background: #f0f5ff;
  box-shadow: 0 2px 8px rgba(24, 144, 255, 0.1);
}

.thinking-step-level-2 {
  background: #f9f0ff;
  border-left: 4px solid #722ed1;
  padding: 12px 16px;
  margin: 8px 0;
  border-radius: 0 8px 8px 0;
  border: 1px solid #efdbff;
  transition: all 0.3s ease;
}

.thinking-step-level-2:hover {
  background: #f0e6ff;
  box-shadow: 0 2px 8px rgba(114, 46, 209, 0.1);
}

/* 步骤标题样式 */
.thinking-step-title-level-1 {
  color: #1890ff;
  font-weight: 600;
}

.thinking-step-title-level-2 {
  color: #722ed1;
  font-weight: 600;
}

/* 层级标识 */
.thinking-level-badge {
  display: inline-flex;
  align-items: center;
  padding: 2px 6px;
  border-radius: 10px;
  font-size: 10px;
  font-weight: bold;
  margin-left: 8px;
}

.thinking-level-badge.level-1 {
  background: #e6f7ff;
  color: #1890ff;
  border: 1px solid #91d5ff;
}

.thinking-level-badge.level-2 {
  background: #f9f0ff;
  color: #722ed1;
  border: 1px solid #d3adf7;
}

/* 触发方式标识 */
.thinking-trigger-badge {
  display: inline-flex;
  align-items: center;
  padding: 2px 6px;
  border-radius: 8px;
  font-size: 10px;
  margin-left: 4px;
}

.thinking-trigger-badge.user-triggered {
  background: #e6f7ff;
  color: #1890ff;
  border: 1px solid #91d5ff;
}

.thinking-trigger-badge.auto-triggered {
  background: #fff7e6;
  color: #fa8c16;
  border: 1px solid #ffd591;
}

/* 思考历史区域 */
.thinking-history {
  margin-top: 24px;
  padding-top: 16px;
  border-top: 1px solid #f0f0f0;
}

.thinking-history-item {
  opacity: 0.7;
  margin-bottom: 12px;
  padding: 8px 12px;
  background: #f5f5f5;
  border-radius: 6px;
  border-left: 3px solid #d9d9d9;
  transition: opacity 0.3s ease;
}

.thinking-history-item:hover {
  opacity: 0.9;
}

.thinking-history-item.level-1 {
  border-left-color: #1890ff;
}

.thinking-history-item.level-2 {
  border-left-color: #722ed1;
}

/* 深度思考状态指示器 */
.thinking-status-enhanced {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  background: linear-gradient(135deg, #f0f5ff 0%, #e6f7ff 100%);
  border-radius: 8px;
  border: 1px solid #d6e4ff;
  margin: 16px 0;
}

.thinking-status-enhanced.level-2 {
  background: linear-gradient(135deg, #f9f0ff 0%, #efdbff 100%);
  border-color: #d3adf7;
}

.thinking-status-icon {
  margin-right: 12px;
  font-size: 20px;
}

.thinking-status-icon.level-1 {
  color: #1890ff;
}

.thinking-status-icon.level-2 {
  color: #722ed1;
}

.thinking-status-text {
  flex: 1;
  font-size: 14px;
  font-weight: 500;
}

.thinking-status-text.level-1 {
  color: #1890ff;
}

.thinking-status-text.level-2 {
  color: #722ed1;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .thinking-level-indicator {
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
  }
  
  .thinking-container-level-1,
  .thinking-container-level-2 {
    margin: 12px 0;
    padding: 12px;
  }
  
  .thinking-step-level-1,
  .thinking-step-level-2 {
    padding: 8px 12px;
  }
}

/* 动画效果 */
.thinking-fade-in {
  animation: thinkingFadeIn 0.5s ease-in-out;
}

@keyframes thinkingFadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.thinking-pulse {
  animation: thinkingPulse 2s ease-in-out infinite;
}

@keyframes thinkingPulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.7;
  }
}
