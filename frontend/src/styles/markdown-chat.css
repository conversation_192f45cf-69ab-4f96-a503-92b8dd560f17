/* 聊天风格Markdown主题 */
.enhanced-markdown-content {
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen, Ubuntu, Cantarell, "Open Sans", "Helvetica Neue", sans-serif;
  font-size: 15px;
  line-height: 1.5;
  color: #333;
}

.enhanced-markdown-content p {
  margin: 0 0 12px 0;
}

.enhanced-markdown-content h1, 
.enhanced-markdown-content h2, 
.enhanced-markdown-content h3 {
  margin-top: 16px;
  margin-bottom: 8px;
  font-weight: 600;
  line-height: 1.25;
}

.enhanced-markdown-content h1 {
  font-size: 1.5em;
  padding-bottom: 4px;
  border-bottom: 1px solid #eaecef;
}

.enhanced-markdown-content h2 {
  font-size: 1.25em;
}

.enhanced-markdown-content h3 {
  font-size: 1.1em;
}

.enhanced-markdown-content code {
  font-family: "SFMono-Regular", <PERSON><PERSON><PERSON>, "Liberation Mono", Menlo, monospace;
  background-color: rgba(0, 0, 0, 0.05);
  padding: 2px 4px;
  border-radius: 3px;
  font-size: 90%;
}

.enhanced-markdown-content pre {
  background-color: #f6f8fa;
  border-radius: 6px;
  padding: 12px;
  overflow: auto;
  margin: 8px 0;
}

.enhanced-markdown-content pre code {
  background-color: transparent;
  padding: 0;
}

.enhanced-markdown-content blockquote {
  border-left: 3px solid #dfe2e5;
  padding: 0 12px;
  color: #6a737d;
  margin: 8px 0;
}

.enhanced-markdown-content a {
  color: #0366d6;
  text-decoration: none;
}

.enhanced-markdown-content a:hover {
  text-decoration: underline;
}

.enhanced-markdown-content ul, 
.enhanced-markdown-content ol {
  padding-left: 24px;
  margin: 8px 0;
}

.enhanced-markdown-content li {
  margin: 4px 0;
}

.enhanced-markdown-content table {
  border-collapse: collapse;
  width: 100%;
  margin: 12px 0;
  font-size: 90%;
}

.enhanced-markdown-content table th {
  background-color: #f6f8fa;
  padding: 8px;
  text-align: left;
  font-weight: 600;
  border: 1px solid #dfe2e5;
}

.enhanced-markdown-content table td {
  padding: 8px;
  border: 1px solid #dfe2e5;
}

.enhanced-markdown-content img {
  max-width: 100%;
  border-radius: 4px;
  margin: 8px 0;
}

/* 思考内容样式优化 */
.thinking-content {
  background-color: #f8f9fa;
  border-left: 3px solid #6c757d;
  padding: 12px;
  border-radius: 4px;
  margin-top: 8px;
  font-size: 14px;
  line-height: 1.5;
  color: #495057;
}

.thinking-header {
  display: flex;
  align-items: center;
  color: #6c757d;
  font-style: italic;
  cursor: pointer;
  user-select: none;
}

.thinking-header:hover {
  color: #495057;
}

/* 代码高亮样式优化 */
.enhanced-markdown-content .syntax-highlighter {
  border-radius: 6px;
  margin: 8px 0;
}

/* 消息气泡样式优化 */
.message-content .enhanced-markdown-content {
  padding: 0;
}

.ai-message-content .enhanced-markdown-content {
  color: inherit;
}
