import { useState } from 'react';
import { Layout, Menu, Switch, Typography } from 'antd';
import { MessageOutlined, SettingOutlined, BulbOutlined } from '@ant-design/icons';
import ChatBox from './components/ChatBox';
import MemoryManager from './components/MemoryManager';
import Settings from './components/Settings';
import { OperationConfirmProvider } from './contexts/OperationConfirmContext';
import { ThinkingProvider } from './contexts/ThinkingContext';
import { SudoPermissionProvider } from './contexts/SudoPermissionContext';
import './App.css';
import './styles/thinking.css';

const { Header, Sider, Content } = Layout;
const { Title } = Typography;

function App() {
  const [collapsed, setCollapsed] = useState(false);
  const [thinkingMode, setThinkingMode] = useState(false);
  const [useTools, setUseTools] = useState(false);
  const [activeMenu, setActiveMenu] = useState('1');

  const renderContent = () => {
    switch (activeMenu) {
      case '1':
        return <ChatBox thinkingMode={thinkingMode} useTools={useTools} />;
      case '2':
        return <MemoryManager />;
      case '3':
        return <Settings />;
      default:
        return (
          <div className="card">
            <Title level={2}>欢迎使用 Sparkle LLM 平台</Title>
            <p>
              Sparkle是一个强大、能独立完成任务、具备记忆能力的LLM平台。
            </p>
            <p>
              您可以在左侧菜单中选择功能，开始使用平台。
            </p>
          </div>
        );
    }
  };

  return (
    <OperationConfirmProvider>
      <ThinkingProvider>
        <SudoPermissionProvider>
          <Layout style={{ minHeight: '100vh' }}>
          <Sider
          collapsible
          collapsed={collapsed}
          onCollapse={setCollapsed}
          theme="light"
        >
        <div className="logo" style={{ padding: '16px', textAlign: 'center', display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
          <Title level={4} style={{ margin: 0, color: '#1890ff' }}>
            {collapsed ? 'S' : 'Sparkle'}
          </Title>
        </div>
        <Menu
          theme="light"
          mode="inline"
          defaultSelectedKeys={['1']}
          selectedKeys={[activeMenu]}
          onClick={({ key }) => setActiveMenu(key)}
          items={[
            {
              key: '1',
              icon: <MessageOutlined />,
              label: '对话',
            },
            {
              key: '2',
              icon: <BulbOutlined />,
              label: '记忆',
            },
            {
              key: '3',
              icon: <SettingOutlined />,
              label: '设置',
            },
          ]}
        />
      </Sider>
      <Layout>
        <Header style={{ padding: '0 16px', background: '#fff', display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
          <Title level={4} style={{ margin: 0 }}>Sparkle LLM 平台</Title>
          <div style={{ display: 'flex', alignItems: 'center' }}>
            <span style={{ marginRight: '8px' }}>深度思考模式</span>
            <Switch
              checked={thinkingMode}
              onChange={setThinkingMode}
              checkedChildren="开启"
              unCheckedChildren="关闭"
              style={{ marginRight: '16px' }}
            />
            {thinkingMode && (
              <>
                <span style={{ marginRight: '8px' }}>使用工具</span>
                <Switch
                  checked={useTools}
                  onChange={setUseTools}
                  checkedChildren="开启"
                  unCheckedChildren="关闭"
                />
              </>
            )}
          </div>
        </Header>
        <Content style={{ margin: '24px 16px', padding: 24, background: '#fff', minHeight: 280 }}>
          {renderContent()}
        </Content>
      </Layout>
        </Layout>
        </SudoPermissionProvider>
      </ThinkingProvider>
    </OperationConfirmProvider>
  );
}

export default App;
