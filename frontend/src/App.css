#root {
  width: 100%;
  height: 100%;
  margin: 0;
  padding: 0;
}

.logo {
  height: 32px;
  margin: 16px;
  background: rgba(24, 144, 255, 0.1);
  border-radius: 6px;
  transition: all 0.3s;
  display: flex;
  align-items: center;
  justify-content: center;
}

.site-layout-background {
  background: #fff;
}

.ant-layout {
  min-height: 100vh;
}

.ant-layout-sider {
  box-shadow: 2px 0 8px 0 rgba(29, 35, 41, 0.05);
}

.ant-layout-header {
  box-shadow: 0 1px 4px rgba(0, 21, 41, 0.08);
  z-index: 1;
}

.card {
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  padding: 20px;
  margin-bottom: 20px;
}

.chat-container {
  display: flex;
  flex-direction: column;
  height: calc(100vh - 200px);
}

.chat-messages {
  flex: 1;
  overflow-y: auto;
  padding: 16px;
}

.chat-input {
  padding: 16px;
  border-top: 1px solid #f0f0f0;
}

.message {
  margin-bottom: 16px;
  display: flex;
}

.message-user {
  justify-content: flex-end;
}

.message-content {
  max-width: 70%;
  padding: 12px;
  border-radius: 8px;
  background-color: #f0f0f0;
}

.message-user .message-content {
  background-color: #1890ff;
  color: white;
}

/* AI消息内容样式 */
.ai-message-content {
  line-height: 1.6;
}

/* Markdown样式 */
.ai-message-content h1 {
  font-size: 1.5em;
  margin-top: 0.5em;
  margin-bottom: 0.5em;
}

.ai-message-content h2 {
  font-size: 1.3em;
  margin-top: 0.5em;
  margin-bottom: 0.5em;
}

.ai-message-content h3 {
  font-size: 1.1em;
  margin-top: 0.5em;
  margin-bottom: 0.5em;
}

.ai-message-content p {
  margin-bottom: 0.8em;
}

.ai-message-content ul, .ai-message-content ol {
  margin-left: 1.5em;
  margin-bottom: 0.8em;
}

.ai-message-content li {
  margin-bottom: 0.3em;
}

.ai-message-content pre {
  margin-bottom: 1em;
  border-radius: 4px;
  overflow: auto;
}

.ai-message-content code {
  font-family: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, Courier, monospace;
  background-color: rgba(0, 0, 0, 0.05);
  padding: 0.2em 0.4em;
  border-radius: 3px;
  font-size: 0.9em;
}

.ai-message-content pre code {
  background-color: transparent;
  padding: 0;
  border-radius: 0;
}

.ai-message-content blockquote {
  border-left: 4px solid #dfe2e5;
  padding-left: 1em;
  color: #6a737d;
  margin: 1em 0;
}

.ai-message-content table {
  border-collapse: collapse;
  width: 100%;
  margin-bottom: 1em;
}

.ai-message-content img {
  max-width: 100%;
  height: auto;
}

/* 图片预览样式 */
.image-preview-mask {
  font-size: 14px;
  color: #fff;
  background-color: rgba(0, 0, 0, 0.5);
  border-radius: 4px;
  padding: 4px 8px;
}

/* 图片容器样式 */
.ant-image {
  display: inline-block;
  margin: 8px 0;
}

/* 图片组样式 */
.ant-image-preview-group {
  text-align: center;
}

/* 图片预览操作栏样式 */
.ant-image-preview-operations {
  background-color: rgba(0, 0, 0, 0.7);
}

.conversation-history {
  height: 100%;
  overflow-y: auto;
}

.active-conversation {
  border-left: 3px solid #1890ff;
}

.chat-header {
  padding: 0 16px;
  border-bottom: 1px solid #f0f0f0;
}
