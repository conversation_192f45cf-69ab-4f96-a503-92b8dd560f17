import React, { useState, useEffect } from 'react';
import { Alert, Tag, Collapse, Typography } from 'antd';
import { BrainOutlined, RobotOutlined, QuestionCircleOutlined } from '@ant-design/icons';

const { Panel } = Collapse;
const { Text } = Typography;

interface AutoThinkingIndicatorProps {
  visible: boolean;
  reason?: string;
  question?: string;
  level?: number;
  style?: React.CSSProperties;
}

/**
 * AI自主启动深度思考指示器
 * 当AI通过工具自主启动深度思考时显示
 */
const AutoThinkingIndicator: React.FC<AutoThinkingIndicatorProps> = ({
  visible,
  reason,
  question,
  level = 1,
  style
}) => {
  const [showDetails, setShowDetails] = useState(false);

  // 当组件变为可见时，自动展开详情（3秒后自动收起）
  useEffect(() => {
    if (visible) {
      setShowDetails(true);
      const timer = setTimeout(() => {
        setShowDetails(false);
      }, 5000);
      return () => clearTimeout(timer);
    }
  }, [visible]);

  if (!visible) {
    return null;
  }

  const getLevelColor = () => {
    switch (level) {
      case 1:
        return '#1890ff';
      case 2:
        return '#722ed1';
      default:
        return '#fa8c16';
    }
  };

  const getLevelText = () => {
    switch (level) {
      case 1:
        return '第一层深度思考';
      case 2:
        return '第二层深度思考';
      default:
        return '深度思考';
    }
  };

  return (
    <div style={{ margin: '16px 0', ...style }}>
      <Alert
        message={
          <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
            <div style={{ display: 'flex', alignItems: 'center' }}>
              <RobotOutlined style={{ fontSize: '16px', marginRight: '8px', color: getLevelColor() }} />
              <Text strong style={{ color: getLevelColor() }}>
                AI自主启动{getLevelText()}
              </Text>
              <Tag color={level === 2 ? 'purple' : 'blue'} style={{ marginLeft: '8px' }}>
                L{level}
              </Tag>
            </div>
            <BrainOutlined style={{ fontSize: '18px', color: getLevelColor() }} />
          </div>
        }
        description={
          <div>
            <Text type="secondary" style={{ fontSize: '12px' }}>
              AI判断当前问题需要深度分析，已自动切换到深度思考模式
            </Text>
            
            {(reason || question) && (
              <Collapse
                ghost
                size="small"
                activeKey={showDetails ? ['details'] : []}
                onChange={(keys) => setShowDetails(keys.includes('details'))}
                style={{ marginTop: '8px' }}
              >
                <Panel
                  header={
                    <Text style={{ fontSize: '12px', color: '#666' }}>
                      <QuestionCircleOutlined style={{ marginRight: '4px' }} />
                      查看详情
                    </Text>
                  }
                  key="details"
                  showArrow={true}
                >
                  <div style={{ padding: '8px 0' }}>
                    {reason && (
                      <div style={{ marginBottom: '8px' }}>
                        <Text strong style={{ fontSize: '12px', color: '#666' }}>
                          启动原因：
                        </Text>
                        <div style={{ 
                          marginTop: '4px', 
                          padding: '6px 8px', 
                          backgroundColor: '#f5f5f5', 
                          borderRadius: '4px',
                          fontSize: '12px'
                        }}>
                          {reason}
                        </div>
                      </div>
                    )}
                    
                    {question && (
                      <div>
                        <Text strong style={{ fontSize: '12px', color: '#666' }}>
                          分析问题：
                        </Text>
                        <div style={{ 
                          marginTop: '4px', 
                          padding: '6px 8px', 
                          backgroundColor: '#f0f5ff', 
                          borderRadius: '4px',
                          fontSize: '12px',
                          border: '1px solid #d6e4ff'
                        }}>
                          {question}
                        </div>
                      </div>
                    )}
                  </div>
                </Panel>
              </Collapse>
            )}
          </div>
        }
        type="info"
        showIcon={false}
        style={{
          backgroundColor: level === 2 ? '#f9f0ff' : '#f0f5ff',
          border: `1px solid ${level === 2 ? '#d3adf7' : '#91d5ff'}`,
          borderRadius: '8px'
        }}
      />
    </div>
  );
};

export default AutoThinkingIndicator;
