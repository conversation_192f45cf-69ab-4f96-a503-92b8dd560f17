import { useState, useRef, useEffect } from 'react';
import { Input, Button, Avatar, Spin, Typography, Steps, Drawer, Tag, message } from 'antd';
import { SendOutlined, RobotOutlined, HistoryOutlined, PlusOutlined, ToolOutlined, QuestionCircleOutlined, DownOutlined, RightOutlined } from '@ant-design/icons';
import { socketService, ThinkingStep, ThinkingStepOperation, ChatMessage } from '../services/socketService';
import { useThinking } from '../contexts/ThinkingContext';
import ConversationHistory from './ConversationHistory';
import EnhancedMarkdownContent from './EnhancedMarkdownContent';
import MessageItem from './MessageItem';
import TaskExecutionPanel from './TaskExecutionPanel';
import ThinkingStatus from './ThinkingStatus';
import AutoThinkingIndicator from './AutoThinkingIndicator';
import { Conversation, ConversationMessage, FrontendMessage as Message } from '../types/conversation';
import { convertToFrontendMessage } from '../utils/conversationUtils';
import '../styles/thinking-status.css';

const { TextArea } = Input;
const { Text } = Typography;
const { Step } = Steps;

interface ChatBoxProps {
  thinkingMode: boolean;
  useTools?: boolean;
}

// 获取API URL
const API_URL = import.meta.env.VITE_API_URL || 'http://localhost:3001';

const ChatBox: React.FC<ChatBoxProps> = ({ thinkingMode, useTools = false }) => {
  const [messages, setMessages] = useState<Message[]>([]);
  const [inputValue, setInputValue] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [isThinking, setIsThinking] = useState(false); // 思考状态
  const [historyDrawerVisible, setHistoryDrawerVisible] = useState(false);
  const [activeConversation, setActiveConversation] = useState<Conversation | null>(null);
  const [taskPanelVisible, setTaskPanelVisible] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);

  // 使用ThinkingContext
  const {
    thinkingSteps,
    setThinkingSteps,
    expandedSteps,
    setExpandedSteps,
    clearThinkingState
  } = useThinking();

  // 初始化Socket连接和注册事件监听器
  useEffect(() => {
    // 初始化Socket连接
    socketService.init();

    // 监听消息
    const messageUnsubscribe = socketService.onMessage((message: ChatMessage) => {
      const newMessage: Message = {
        id: message.id || Date.now().toString(),
        content: message.content,
        sender: message.sender,
        timestamp: new Date(message.timestamp),
        tool_calls: message.tool_calls,
        tool_results: message.tool_results,
      };

      // 如果是AI消息且包含工具调用结果，则替换之前的工具调用状态消息
      if (message.sender === 'ai' && message.tool_calls && message.tool_calls.length > 0 && message.tool_results) {
        setMessages((prev) => {
          const updatedMessages = [...prev];
          // 查找并移除工具调用中和工具调用完成的消息
          const toolMessageIndex = updatedMessages.findIndex(
            msg => (msg.status === 'calling' || msg.status === 'called') && msg.sender === 'ai'
          );

          if (toolMessageIndex !== -1) {
            // 替换该消息为最终结果
            updatedMessages[toolMessageIndex] = newMessage;
            return updatedMessages;
          } else {
            // 如果没有找到，添加新消息
            return [...prev, newMessage];
          }
        });
      } else {
        // 普通消息直接添加
        setMessages((prev) => [...prev, newMessage]);
      }

      setIsLoading(false);
    });

    // 监听工具调用中状态
    const toolCallingUnsubscribe = socketService.onToolCalling((message: ChatMessage) => {
      const newMessage: Message = {
        id: `tool-calling-${Date.now()}`,
        content: message.content,
        sender: message.sender,
        timestamp: new Date(message.timestamp),
        tool_calls: message.tool_calls,
        tool_results: [],
        status: 'calling' as any
      };
      setMessages((prev) => [...prev, newMessage]);
    });

    // 监听工具调用完成状态
    const toolCalledUnsubscribe = socketService.onToolCalled((message: ChatMessage) => {
      // 查找并更新工具调用中的消息
      setMessages((prev) => {
        const updatedMessages = [...prev];
        // 查找最近的工具调用中消息
        const callingIndex = updatedMessages.findIndex(
          msg => msg.status === 'calling' && msg.sender === 'ai'
        );

        if (callingIndex !== -1) {
          // 更新该消息
          updatedMessages[callingIndex] = {
            ...updatedMessages[callingIndex],
            content: "正在生成最终结果，请稍候...",
            tool_results: message.tool_results,
            tool_calls: message.tool_calls,
            status: 'called' as any
          };
          return updatedMessages;
        } else {
          // 如果没有找到，添加新消息
          const newMessage: Message = {
            id: `tool-called-${Date.now()}`,
            content: "正在生成最终结果，请稍候...",
            sender: message.sender,
            timestamp: new Date(message.timestamp),
            tool_calls: message.tool_calls,
            tool_results: message.tool_results,
            status: 'called' as any
          };
          return [...prev, newMessage];
        }
      });
    });

    // 监听思考步骤
    const stepsUnsubscribe = socketService.onThinkingSteps((steps: ThinkingStep[]) => {
      setThinkingSteps(steps);

      // 更新折叠状态
      const newExpandedSteps: Record<string, boolean> = {};
      steps.forEach((step, index) => {
        const isLastStep = index === steps.length - 1;
        const isWaitingForUser = step.status === 'waiting_for_user';

        // 最后一个步骤或等待用户输入的步骤默认展开，其他步骤默认折叠
        // 保留用户手动设置的展开状态
        if (expandedSteps[step.id] !== undefined) {
          newExpandedSteps[step.id] = expandedSteps[step.id];
        } else {
          newExpandedSteps[step.id] = isLastStep || isWaitingForUser;
        }
      });

      setExpandedSteps(newExpandedSteps);
    });

    // 监听思考步骤操作更新
    const stepOperationUnsubscribe = socketService.onThinkingStepOperation((operation: ThinkingStepOperation) => {
      // 更新单个步骤的操作日志和当前操作
      setThinkingSteps(prevSteps => {
        return prevSteps.map(step => {
          if (step.id === operation.stepId) {
            return {
              ...step,
              operations_log: operation.operations_log,
              current_operation: operation.current_operation
            };
          }
          return step;
        });
      });
    });

    // 监听错误
    const errorUnsubscribe = socketService.onError((error) => {
      console.error('Socket错误:', error);
      setIsLoading(false);
    });

    // 监听对话创建
    const conversationCreatedUnsubscribe = socketService.onConversationCreated((conversation) => {
      setActiveConversation(conversation);
      setMessages([]);
      // 清除深度思考状态，避免在新对话中显示上一个对话的思考步骤
      clearThinkingState();
    });

    // 监听对话激活
    const conversationActivatedUnsubscribe = socketService.onConversationActivated((conversation) => {
      setActiveConversation(conversation);
      loadConversationMessages(conversation.id);
      // 清除深度思考状态，避免在切换对话时显示上一个对话的思考步骤
      clearThinkingState();
    });

    // 监听对话更新
    const conversationUpdatedUnsubscribe = socketService.onConversationUpdated((conversation) => {
      setActiveConversation(conversation);
    });

    // 监听任务进度事件
    const taskProgressUnsubscribe = socketService.onTaskProgress((_) => {
      // 当收到任务进度事件时，显示任务执行面板
      setTaskPanelVisible(true);
    });

    // 监听思考开始事件
    const thinkingStartUnsubscribe = socketService.onThinkingStart(() => {
      setIsThinking(true);
    });

    // 监听思考结束事件
    const thinkingEndUnsubscribe = socketService.onThinkingEnd(() => {
      setIsThinking(false);
    });

    // 获取当前活动对话
    fetchActiveConversation();

    // 组件卸载时清理事件监听器
    return () => {
      messageUnsubscribe();
      toolCallingUnsubscribe();
      toolCalledUnsubscribe();
      stepsUnsubscribe();
      stepOperationUnsubscribe(); // 清理思考步骤操作更新监听器
      errorUnsubscribe();
      conversationCreatedUnsubscribe();
      conversationActivatedUnsubscribe();
      conversationUpdatedUnsubscribe();
      taskProgressUnsubscribe();
      thinkingStartUnsubscribe();
      thinkingEndUnsubscribe();
    };
  }, []);

  // 获取当前活动对话
  const fetchActiveConversation = async () => {
    try {
      const response = await fetch(`${API_URL}/api/conversations/active`);

      // 检查响应状态
      if (!response.ok) {
        throw new Error(`HTTP错误: ${response.status} ${response.statusText}`);
      }

      // 检查内容类型
      const contentType = response.headers.get('content-type');
      if (!contentType || !contentType.includes('application/json')) {
        throw new Error(`预期JSON响应，但收到: ${contentType}`);
      }

      const data = await response.json();

      if (data.success && data.data) {
        setActiveConversation(data.data);
        loadConversationMessages(data.data.id);
      } else {
        // 如果没有活动对话，创建一个新对话
        socketService.createConversation();
      }
    } catch (error) {
      console.error('获取活动对话失败:', error);
    }
  };

  // 加载对话消息
  const loadConversationMessages = async (conversationId: number) => {
    try {
      const response = await fetch(`${API_URL}/api/conversations/${conversationId}`);

      // 检查响应状态
      if (!response.ok) {
        throw new Error(`HTTP错误: ${response.status} ${response.statusText}`);
      }

      // 检查内容类型
      const contentType = response.headers.get('content-type');
      if (!contentType || !contentType.includes('application/json')) {
        throw new Error(`预期JSON响应，但收到: ${contentType}`);
      }

      const data = await response.json();

      if (data.success) {
        // 转换消息格式，使用统一的转换函数
        const conversationMessages = (data.data.messages || []).map((msg: ConversationMessage) =>
          convertToFrontendMessage(msg)
        );

        // 设置消息
        setMessages(conversationMessages);
      }
    } catch (error) {
      console.error('加载对话消息失败:', error);
    }
  };

  // 自动滚动到底部
  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);



  // 发送消息
  const handleSendMessage = () => {
    if (!inputValue.trim()) return;

    setIsLoading(true);
    // 手动设置思考状态，确保在后端发送事件前就显示思考状态
    setIsThinking(true);

    if (thinkingMode) {
      // 发送深度思考消息，包含是否使用工具的选项
      socketService.sendThinkingMessage(inputValue, useTools);
      // 清除之前的思考步骤和折叠状态
      clearThinkingState();
    } else {
      // 发送普通消息
      socketService.sendMessage(inputValue);
    }

    setInputValue('');
  };

  // 创建新对话
  const handleNewConversation = () => {
    socketService.createConversation();
    setHistoryDrawerVisible(false);
    // 清除深度思考状态，避免在新对话中显示上一个对话的思考步骤
    clearThinkingState();
  };

  // 选择对话
  const handleSelectConversation = (conversationId: number) => {
    socketService.activateConversation(conversationId);
    setHistoryDrawerVisible(false);
    // 清除深度思考状态，避免在切换对话时显示上一个对话的思考步骤
    clearThinkingState();
  };

  return (
    <div className="chat-container">
      <div className="chat-header" style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '16px' }}>
        <div>
          {activeConversation && (
            <Text strong style={{ fontSize: '16px' }}>{activeConversation.title}</Text>
          )}
        </div>
        <div>
          <Button
            type="primary"
            icon={<PlusOutlined />}
            onClick={handleNewConversation}
            style={{ marginRight: '8px' }}
          >
            新对话
          </Button>
          <Button
            icon={<HistoryOutlined />}
            onClick={() => setHistoryDrawerVisible(true)}
          >
            历史记录
          </Button>
        </div>
      </div>

      <div className="chat-messages">
        {messages.map((message) => (
          <MessageItem key={message.id} message={message} />
        ))}

        {/* 思考步骤显示 - 无论是用户启动还是AI自主启动都要显示 */}
        {(thinkingMode || isThinking) && thinkingSteps.length > 0 && (
          <div className="thinking-steps" style={{ margin: '16px 0' }}>
            {/* 如果不是用户主动开启的深度思考模式，显示AI自主启动的提示 */}
            <AutoThinkingIndicator
              visible={!thinkingMode && isThinking}
              level={1}
            />
            <Steps
              direction="vertical"
              size="small"
              current={thinkingSteps.filter(step => step.status === 'process' || step.status === 'completed').length}
            >
              {thinkingSteps.map((step) => (
                <Step
                  key={step.id}
                  title={
                    <div
                      style={{
                        display: 'flex',
                        alignItems: 'center',
                        cursor: 'pointer',
                        padding: '8px 0',
                        // 最后一个步骤使用更明显的样式
                        ...(step.content === '生成最终回答' && step.status === 'completed' ? {
                          fontWeight: 'bold',
                          fontSize: '16px',
                          color: '#1890ff'
                        } : {})
                      }}
                      onClick={() => {
                        // 切换当前步骤的折叠状态
                        setExpandedSteps(prev => ({
                          ...prev,
                          [step.id]: !prev[step.id]
                        }));
                      }}
                    >
                      {/* 折叠/展开图标 */}
                      {expandedSteps[step.id] ? (
                        <DownOutlined style={{ marginRight: '8px' }} />
                      ) : (
                        <RightOutlined style={{ marginRight: '8px' }} />
                      )}

                      <span>{step.content}</span>

                      {/* 显示当前操作状态 */}
                      {step.current_operation && step.status === 'process' && (
                        <Tag color="processing" style={{ marginLeft: '8px' }}>
                          {step.current_operation}
                        </Tag>
                      )}

                      {step.has_tools && (
                        <Tag color="blue" style={{ marginLeft: '8px' }}>
                          <ToolOutlined /> 工具
                        </Tag>
                      )}

                      {step.status === 'process' && (
                        <Spin size="small" style={{ marginLeft: '8px' }} />
                      )}

                      {step.status === 'waiting_for_user' && (
                        <Tag color="orange" style={{ marginLeft: '8px' }}>
                          <QuestionCircleOutlined /> 等待回答
                        </Tag>
                      )}

                      {/* 显示步骤状态的简短描述 */}
                      {step.status === 'completed' && step.content !== '生成最终回答' && (
                        <Text type="secondary" style={{ marginLeft: '8px', fontSize: '12px' }}>
                          {expandedSteps[step.id] ? '(点击折叠)' : '(点击展开)'}
                        </Text>
                      )}
                    </div>
                  }
                  description={
                    <>
                      {/* 只有在展开状态或等待用户输入时才显示内容 */}
                      {expandedSteps[step.id] && (
                        <div style={{
                          padding: '8px 0',
                          // 最终答案使用特殊样式
                          ...(step.content === '生成最终回答' && step.status === 'completed' ? {
                            border: '1px solid #d9d9d9',
                            borderRadius: '8px',
                            padding: '16px',
                            backgroundColor: '#f9f9f9',
                            marginTop: '8px'
                          } : {})
                        }}>
                          {/* 显示步骤的详细目标 */}
                          {step.objective && step.content !== '生成最终回答' && (
                            <div style={{
                              marginBottom: '12px',
                              padding: '12px',
                              backgroundColor: '#f6ffed',
                              border: '1px solid #b7eb8f',
                              borderRadius: '4px'
                            }}>
                              <Typography.Text strong style={{ fontSize: '14px' }}>
                                步骤目标:
                              </Typography.Text>
                              <div style={{ marginTop: '4px' }}>
                                <EnhancedMarkdownContent content={step.objective} />
                              </div>
                            </div>
                          )}

                          {/* 显示操作日志 */}
                          {step.operations_log && step.operations_log.length > 0 && (
                            <div style={{
                              marginBottom: '12px',
                              padding: '12px',
                              backgroundColor: '#f0f5ff',
                              border: '1px solid #d6e4ff',
                              borderRadius: '4px'
                            }}>
                              <Typography.Text strong style={{ fontSize: '14px' }}>
                                操作日志:
                              </Typography.Text>
                              <ul style={{ marginTop: '4px', paddingLeft: '20px' }}>
                                {step.operations_log.map((log, index) => (
                                  <li key={index} style={{ marginBottom: '4px', fontSize: '13px' }}>
                                    {log}
                                  </li>
                                ))}
                              </ul>
                            </div>
                          )}

                          {/* 显示工具调用详情 */}
                          {step.tool_calls && step.tool_calls.length > 0 && (
                            <div style={{
                              marginBottom: '12px',
                              padding: '12px',
                              backgroundColor: '#f9f0ff',
                              border: '1px solid #efdbff',
                              borderRadius: '4px'
                            }}>
                              <Typography.Text strong style={{ fontSize: '14px' }}>
                                工具调用详情:
                              </Typography.Text>
                              <div style={{ marginTop: '8px' }}>
                                {step.tool_calls.map((tool, index) => {
                                  const toolResult = step.tool_results ? step.tool_results[index] : null;
                                  const hasError = toolResult && toolResult.error;

                                  return (
                                    <div key={index} style={{
                                      marginBottom: '8px',
                                      padding: '8px',
                                      backgroundColor: hasError ? '#fff2f0' : '#f6ffed',
                                      border: `1px solid ${hasError ? '#ffccc7' : '#b7eb8f'}`,
                                      borderRadius: '4px'
                                    }}>
                                      <div style={{ fontWeight: 'bold', marginBottom: '4px' }}>
                                        <ToolOutlined /> {tool.name}
                                        {hasError ?
                                          <Tag color="error" style={{ marginLeft: '8px' }}>失败</Tag> :
                                          <Tag color="success" style={{ marginLeft: '8px' }}>成功</Tag>
                                        }
                                      </div>

                                      {hasError && (
                                        <div style={{ color: '#f5222d', marginTop: '4px' }}>
                                          错误: {toolResult.error}
                                        </div>
                                      )}
                                    </div>
                                  );
                                })}
                              </div>
                            </div>
                          )}

                          {/* 显示步骤结果 */}
                          {step.result && (
                            <>
                              <EnhancedMarkdownContent content={step.result} />

                              {/* 添加结论部分的特殊样式，使其更加突出 */}
                              {step.result.includes('结论:') && step.status === 'completed' && (
                                <div style={{
                                  marginTop: '12px',
                                  padding: '12px',
                                  backgroundColor: '#e6f7ff',
                                  border: '1px solid #91d5ff',
                                  borderRadius: '4px'
                                }}>
                                  <Typography.Text type="secondary" style={{ fontSize: '12px' }}>
                                    此步骤结论将用于生成最终答案，包含该步骤的关键发现
                                  </Typography.Text>
                                </div>
                              )}
                            </>
                          )}
                        </div>
                      )}

                      {step.status === 'waiting_for_user' && step.question && (
                        <div style={{ marginTop: '16px', border: '1px solid #f0f0f0', padding: '16px', borderRadius: '8px', backgroundColor: '#fafafa' }}>
                          <div style={{ fontWeight: 'bold', marginBottom: '8px' }}>问题：{step.question}</div>

                          <Input.TextArea
                            placeholder="请输入你的回答..."
                            autoSize={{ minRows: 2, maxRows: 6 }}
                            value={step.user_answer || ''}
                            onChange={(e) => {
                              // 更新本地状态中的用户回答
                              const updatedSteps = thinkingSteps.map(s =>
                                s.id === step.id ? { ...s, user_answer: e.target.value } : s
                              );
                              setThinkingSteps(updatedSteps);
                            }}
                            style={{ marginBottom: '8px' }}
                          />

                          <Button
                            type="primary"
                            onClick={() => {
                              if (step.user_answer && step.user_answer.trim()) {
                                // 发送用户回答
                                socketService.sendThinkingUserAnswer(step.id, step.user_answer);
                              } else {
                                message.warning('请输入回答后再提交');
                              }
                            }}
                          >
                            提交回答
                          </Button>
                        </div>
                      )}
                    </>
                  }
                  status={
                    step.status === 'completed'
                      ? 'finish'
                      : step.status === 'error'
                        ? 'error'
                        : step.status === 'process'
                          ? 'process'
                          : step.status === 'waiting_for_user'
                            ? 'process'
                            : 'wait'
                  }
                />
              ))}
            </Steps>
          </div>
        )}

        {/* 思考状态组件 */}
        <ThinkingStatus visible={isThinking} />

        {/* 旧的加载状态，保留以兼容旧代码 */}
        {isLoading && !isThinking && (
          <div className="message">
            <Avatar
              icon={<RobotOutlined />}
              style={{ backgroundColor: '#1890ff', marginRight: '8px' }}
            />
            <div className="message-content">
              <Spin size="small" /> <Text type="secondary">思考中...</Text>
            </div>
          </div>
        )}
        <div ref={messagesEndRef} />
      </div>
      <div className="chat-input">
        <TextArea
          value={inputValue}
          onChange={(e) => setInputValue(e.target.value)}
          placeholder="请输入您的问题..."
          autoSize={{ minRows: 2, maxRows: 6 }}
          onPressEnter={(e) => {
            if (!e.shiftKey) {
              e.preventDefault();
              handleSendMessage();
            }
          }}
        />
        <Button
          type="primary"
          icon={<SendOutlined />}
          onClick={handleSendMessage}
          style={{ marginTop: '8px', float: 'right' }}
          loading={isLoading}
        >
          发送
        </Button>
        <Text type="secondary" style={{ marginTop: '12px', display: 'inline-block' }}>
          按 Enter 发送，Shift + Enter 换行
          {thinkingMode && (
            <>
              {' (深度思考模式已开启'}
              {useTools && ', 工具调用已启用'}
              {')'}
            </>
          )}
        </Text>
      </div>

      {/* 对话历史抽屉 */}
      <Drawer
        title="对话历史"
        placement="right"
        onClose={() => setHistoryDrawerVisible(false)}
        open={historyDrawerVisible}
        width={320}
      >
        <ConversationHistory onSelectConversation={handleSelectConversation} />
      </Drawer>

      {/* 任务执行面板 */}
      <TaskExecutionPanel
        visible={taskPanelVisible}
        onClose={() => setTaskPanelVisible(false)}
      />
    </div>
  );
};

export default ChatBox;
