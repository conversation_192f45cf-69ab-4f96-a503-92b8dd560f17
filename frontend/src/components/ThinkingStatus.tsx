import React from 'react';
import { Spin, Typography } from 'antd';
import { LoadingOutlined } from '@ant-design/icons';

const { Text } = Typography;

interface ThinkingStatusProps {
  visible: boolean;
}

/**
 * 思考状态组件
 * 当AI正在思考时显示加载动画和文字提示
 */
const ThinkingStatus: React.FC<ThinkingStatusProps> = ({ visible }) => {
  if (!visible) return null;

  // 自定义加载图标
  const antIcon = <LoadingOutlined style={{ fontSize: 24, color: '#1890ff' }} spin />;

  return (
    <div className="thinking-status">
      <div className="thinking-status-content">
        <Spin indicator={antIcon} />
        <Text className="thinking-text">正在思考...</Text>
      </div>
    </div>
  );
};

export default ThinkingStatus;
