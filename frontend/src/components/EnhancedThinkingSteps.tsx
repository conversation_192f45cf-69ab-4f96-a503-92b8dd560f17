import React from 'react';
import { Steps, Card, Divider } from 'antd';
import { useThinking } from '../contexts/ThinkingContext';
import ThinkingLevelIndicator from './ThinkingLevelIndicator';
import { ThinkingStep } from '../services/socketService';

const { Step } = Steps;

interface EnhancedThinkingStepsProps {
  thinkingMode: boolean;
  style?: React.CSSProperties;
}

/**
 * 增强的思考步骤组件
 * 支持多层级深度思考的可视化展示
 */
const EnhancedThinkingSteps: React.FC<EnhancedThinkingStepsProps> = ({
  thinkingMode,
  style
}) => {
  const {
    thinkingSteps,
    expandedSteps,
    setExpandedSteps,
    thinkingSessions,
    currentSession,
    getCurrentLevel
  } = useThinking();

  if (!thinkingMode || thinkingSteps.length === 0) {
    return null;
  }

  const currentLevel = getCurrentLevel();

  // 渲染思考步骤的函数（从原ChatBox组件提取）
  const renderThinkingSteps = (steps: ThinkingStep[], sessionLevel: number) => {
    return (
      <Steps
        direction="vertical"
        size="small"
        current={steps.filter(step => step.status === 'process' || step.status === 'completed').length}
      >
        {steps.map((step) => (
          <Step
            key={step.id}
            title={
              <div
                style={{
                  display: 'flex',
                  alignItems: 'center',
                  cursor: 'pointer',
                  padding: '8px 0',
                  // 第二层深度思考使用不同的样式
                  ...(sessionLevel === 2 ? {
                    backgroundColor: '#f9f0ff',
                    borderRadius: '4px',
                    padding: '8px 12px',
                    border: '1px solid #efdbff'
                  } : {}),
                  // 最后一个步骤使用更明显的样式
                  ...(step.content === '生成最终回答' && step.status === 'completed' ? {
                    fontWeight: 'bold',
                    fontSize: '16px',
                    color: sessionLevel === 2 ? '#722ed1' : '#1890ff'
                  } : {})
                }}
                onClick={() => {
                  // 切换当前步骤的折叠状态
                  setExpandedSteps(prev => ({
                    ...prev,
                    [step.id]: !prev[step.id]
                  }));
                }}
              >
                {/* 这里可以添加步骤的详细渲染逻辑 */}
                <span>{step.content}</span>
                
                {/* 第二层深度思考的特殊标识 */}
                {sessionLevel === 2 && (
                  <span style={{
                    marginLeft: '8px',
                    fontSize: '10px',
                    color: '#722ed1',
                    fontWeight: 'normal'
                  }}>
                    (L2)
                  </span>
                )}
              </div>
            }
            // 这里可以添加更多的步骤渲染逻辑
            status={
              step.status === 'completed'
                ? 'finish'
                : step.status === 'error'
                  ? 'error'
                  : step.status === 'process'
                    ? 'process'
                    : step.status === 'waiting_for_user'
                      ? 'process'
                      : 'wait'
            }
          />
        ))}
      </Steps>
    );
  };

  return (
    <div style={{ margin: '16px 0', ...style }}>
      {/* 显示当前会话的层级指示器 */}
      {currentSession && (
        <div style={{ marginBottom: '16px' }}>
          <ThinkingLevelIndicator
            level={currentSession.level}
            trigger={currentSession.trigger}
            reason={currentSession.reason}
          />
        </div>
      )}

      {/* 根据层级使用不同的容器样式 */}
      {currentLevel === 2 ? (
        <Card
          size="small"
          style={{
            backgroundColor: '#fafafa',
            border: '2px solid #722ed1',
            borderRadius: '8px'
          }}
          title={
            <div style={{ color: '#722ed1', fontWeight: 'bold' }}>
              第二层深度思考
            </div>
          }
        >
          {renderThinkingSteps(thinkingSteps, currentLevel)}
        </Card>
      ) : (
        <div className="thinking-steps">
          {renderThinkingSteps(thinkingSteps, currentLevel)}
        </div>
      )}

      {/* 显示历史会话（如果有多个会话） */}
      {thinkingSessions.length > 1 && (
        <div style={{ marginTop: '16px' }}>
          <Divider orientation="left" style={{ fontSize: '12px', color: '#8c8c8c' }}>
            思考历史
          </Divider>
          {thinkingSessions
            .filter(session => session.id !== currentSession?.id)
            .map(session => (
              <Card
                key={session.id}
                size="small"
                style={{
                  marginBottom: '8px',
                  opacity: 0.7,
                  backgroundColor: '#f5f5f5'
                }}
                title={
                  <div style={{ fontSize: '12px' }}>
                    <ThinkingLevelIndicator
                      level={session.level}
                      trigger={session.trigger}
                      reason={session.reason}
                      style={{ transform: 'scale(0.9)' }}
                    />
                  </div>
                }
              >
                <div style={{ fontSize: '12px', color: '#8c8c8c' }}>
                  {session.steps.length} 个步骤已完成
                </div>
              </Card>
            ))}
        </div>
      )}
    </div>
  );
};

export default EnhancedThinkingSteps;
