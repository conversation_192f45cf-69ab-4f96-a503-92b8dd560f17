import React from 'react';
import { Tag, Tooltip } from 'antd';
import { BranchesOutlined, DeploymentUnitOutlined } from '@ant-design/icons';

interface ThinkingLevelIndicatorProps {
  level: number;
  trigger: 'user' | 'auto';
  reason?: string;
  style?: React.CSSProperties;
}

/**
 * 深度思考层级指示器组件
 * 显示当前深度思考的层级和触发方式
 */
const ThinkingLevelIndicator: React.FC<ThinkingLevelIndicatorProps> = ({
  level,
  trigger,
  reason,
  style
}) => {
  const getLevelColor = (level: number) => {
    switch (level) {
      case 1:
        return '#1890ff';
      case 2:
        return '#722ed1';
      default:
        return '#8c8c8c';
    }
  };

  const getLevelIcon = (level: number) => {
    switch (level) {
      case 1:
        return <BranchesOutlined />;
      case 2:
        return <DeploymentUnitOutlined />;
      default:
        return <BranchesOutlined />;
    }
  };

  const getTriggerText = (trigger: string) => {
    return trigger === 'user' ? '用户启动' : 'AI自动启动';
  };

  const getTooltipContent = () => {
    let content = `第${level}层深度思考 - ${getTriggerText(trigger)}`;
    if (reason && trigger === 'auto') {
      content += `\n原因: ${reason}`;
    }
    if (level === 2) {
      content += '\n注意: 已达到最大深度思考层级';
    }
    return content;
  };

  return (
    <div style={{ display: 'flex', alignItems: 'center', gap: '8px', ...style }}>
      <Tooltip title={getTooltipContent()} placement="top">
        <Tag
          icon={getLevelIcon(level)}
          color={getLevelColor(level)}
          style={{
            fontSize: '12px',
            padding: '4px 8px',
            borderRadius: '12px',
            border: `2px solid ${getLevelColor(level)}`,
            fontWeight: 'bold'
          }}
        >
          L{level} 深度思考
        </Tag>
      </Tooltip>
      
      <Tag
        color={trigger === 'user' ? 'blue' : 'orange'}
        style={{
          fontSize: '11px',
          padding: '2px 6px',
          borderRadius: '8px'
        }}
      >
        {getTriggerText(trigger)}
      </Tag>

      {reason && trigger === 'auto' && (
        <Tooltip title={`触发原因: ${reason}`} placement="top">
          <Tag
            color="default"
            style={{
              fontSize: '11px',
              padding: '2px 6px',
              borderRadius: '8px',
              maxWidth: '200px',
              overflow: 'hidden',
              textOverflow: 'ellipsis',
              whiteSpace: 'nowrap'
            }}
          >
            {reason}
          </Tag>
        </Tooltip>
      )}
    </div>
  );
};

export default ThinkingLevelIndicator;
