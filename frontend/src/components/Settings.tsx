import { useState, useEffect } from 'react';
import { Form, Input, Button, Select, Switch, Card, Typography, Divider, message, Radio } from 'antd';
import { SaveOutlined, ReloadOutlined, SyncOutlined } from '@ant-design/icons';
import OperationExample from './OperationExample';

const { Title, Text } = Typography;
const { Option } = Select;

interface Model {
  name: string;
}

interface Settings {
  model_provider: string;
  ollama_api_url: string;
  ollama_model: string;
  qwen_api_url: string;
  qwen_api_key: string;
  qwen_model: string;
  qwen_enable_search: string;
  deepseek_api_url: string;
  deepseek_api_key: string;
  deepseek_model: string;
  temperature: string;
  max_tokens: string;
  enable_memory: string;
  memory_importance_threshold: string;
  enable_deep_thinking: string;
  history_window_size: string;
}

interface SettingsProps {}

const Settings: React.FC<SettingsProps> = () => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [modelLoading, setModelLoading] = useState(false);
  const [ollamaModels, setOllamaModels] = useState<Model[]>([]);
  const [modelProvider, setModelProvider] = useState<string>('ollama');

  // 获取设置
  const fetchSettings = async () => {
    setLoading(true);
    try {
      const response = await fetch('http://localhost:3001/api/settings');
      const data = await response.json();

      if (data.success) {
        const settings: Record<string, string> = {};
        data.data.forEach((setting: { key: string; value: string }) => {
          settings[setting.key] = setting.value;
        });

        // 更新表单值
        form.setFieldsValue({
          modelProvider: settings.model_provider || 'ollama',
          ollamaApiUrl: settings.ollama_api_url || 'http://localhost:11434/api',
          ollamaModel: settings.ollama_model || '',
          qwenApiUrl: settings.qwen_api_url || 'https://dashscope.aliyuncs.com/compatible-mode/v1/chat/completions',
          qwenApiKey: settings.qwen_api_key || '',
          qwenModel: settings.qwen_model || 'qwen-max',
          qwenEnableSearch: settings.qwen_enable_search === 'true',
          deepseekApiUrl: settings.deepseek_api_url || 'https://api.deepseek.com/v1/chat/completions',
          deepseekApiKey: settings.deepseek_api_key || '',
          deepseekModel: settings.deepseek_model || 'deepseek-chat',
          temperature: settings.temperature || '0.7',
          maxTokens: settings.max_tokens || '2048',
          enableMemory: settings.enable_memory === 'true',
          memoryImportanceThreshold: settings.memory_importance_threshold || '0.5',
          enableDeepThinking: settings.enable_deep_thinking === 'true',
          historyWindowSize: settings.history_window_size || '5',
        });

        // 更新模型提供商状态
        setModelProvider(settings.model_provider || 'ollama');
      }
    } catch (error) {
      console.error('获取设置失败:', error);
      message.error('获取设置失败');
    } finally {
      setLoading(false);
    }
  };

  // 获取Ollama模型列表
  const fetchOllamaModels = async () => {
    setModelLoading(true);
    try {
      // 明确指定provider=ollama，确保始终获取Ollama模型列表
      const response = await fetch('http://localhost:3001/api/model/models?provider=ollama');
      const data = await response.json();

      if (data.success) {
        setOllamaModels(data.data);

        // 如果没有模型，使用默认模型
        if (data.data.length === 0) {
          // 获取默认模型配置
          fetch('http://localhost:3001/api/model/config')
            .then(response => response.json())
            .then(configData => {
              if (configData.success && configData.data.ollama) {
                form.setFieldsValue({ ollamaModel: configData.data.ollama.model });
              }
            })
            .catch(error => {
              console.error('获取默认模型配置失败:', error);
              form.setFieldsValue({ ollamaModel: '' });
            });
        } else {
          // 如果当前选择的模型不在列表中，选择第一个可用模型
          const currentModel = form.getFieldValue('ollamaModel');
          const modelExists = data.data.some((model: Model) => model.name === currentModel);

          if (!modelExists && data.data.length > 0) {
            form.setFieldsValue({ ollamaModel: data.data[0].name });
          }
        }
      } else {
        // 如果API返回错误，获取默认模型配置
        fetch('http://localhost:3001/api/model/config')
          .then(response => response.json())
          .then(configData => {
            if (configData.success && configData.data.ollama) {
              form.setFieldsValue({ ollamaModel: configData.data.ollama.model });
            }
          })
          .catch(error => {
            console.error('获取默认模型配置失败:', error);
            form.setFieldsValue({ ollamaModel: '' });
          });
        message.warning('获取Ollama模型列表失败，将使用默认模型');
      }
    } catch (error) {
      console.error('获取Ollama模型列表失败:', error);
      message.error('获取Ollama模型列表失败');
      // 获取默认模型配置
      fetch('http://localhost:3001/api/model/config')
        .then(response => response.json())
        .then(configData => {
          if (configData.success && configData.data.ollama) {
            form.setFieldsValue({ ollamaModel: configData.data.ollama.model });
          }
        })
        .catch(error => {
          console.error('获取默认模型配置失败:', error);
          form.setFieldsValue({ ollamaModel: '' });
        });
    } finally {
      setModelLoading(false);
    }
  };

  // 组件加载时获取设置和模型列表
  useEffect(() => {
    fetchSettings();
    fetchOllamaModels();
  }, []);

  // 处理模型提供商变更
  const handleModelProviderChange = (value: string) => {
    setModelProvider(value);
  };

  // 保存设置
  const handleSave = async (values: any) => {
    setLoading(true);
    try {
      // 转换设置格式
      const settings = {
        model_provider: values.modelProvider,
        ollama_api_url: values.ollamaApiUrl,
        ollama_model: values.ollamaModel,
        qwen_api_url: values.qwenApiUrl,
        qwen_api_key: values.qwenApiKey,
        qwen_model: values.qwenModel,
        qwen_enable_search: values.qwenEnableSearch ? 'true' : 'false',
        deepseek_api_url: values.deepseekApiUrl,
        deepseek_api_key: values.deepseekApiKey,
        deepseek_model: values.deepseekModel,
        temperature: values.temperature,
        max_tokens: values.maxTokens,
        enable_memory: values.enableMemory ? 'true' : 'false',
        memory_importance_threshold: values.memoryImportanceThreshold,
        enable_deep_thinking: values.enableDeepThinking ? 'true' : 'false',
        history_window_size: values.historyWindowSize,
      };

      // 发送到后端保存设置
      const response = await fetch('http://localhost:3001/api/settings', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ settings }),
      });

      const data = await response.json();

      if (data.success) {
        message.success('设置已保存');
      } else {
        message.error('保存设置失败: ' + data.message);
      }
    } catch (error) {
      console.error('保存设置失败:', error);
      message.error('保存设置失败');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="settings">
      <Title level={3}>系统设置</Title>
      <Text type="secondary">
        配置系统参数和模型设置
      </Text>

      <Form
        form={form}
        layout="vertical"
        onFinish={handleSave}
        initialValues={{
          modelProvider: 'ollama',
          ollamaApiUrl: 'http://localhost:11434/api',
          ollamaModel: ollamaModels.length > 0 ? ollamaModels[0].name : '',
          qwenApiUrl: 'https://dashscope.aliyuncs.com/compatible-mode/v1/chat/completions',
          qwenApiKey: '',
          qwenModel: 'qwen-max',
          qwenEnableSearch: false,
          deepseekApiUrl: 'https://api.deepseek.com/v1/chat/completions',
          deepseekApiKey: '',
          deepseekModel: 'deepseek-chat',
          temperature: 0.7,
          maxTokens: 2048,
          enableMemory: true,
          memoryImportanceThreshold: 0.5,
          enableDeepThinking: true,
          historyWindowSize: 5,
        }}
      >
        <Card title="模型提供商" style={{ marginTop: 20 }}>
          <Form.Item
            name="modelProvider"
            label="选择模型提供商"
            rules={[{ required: true, message: '请选择模型提供商' }]}
          >
            <Radio.Group onChange={(e) => handleModelProviderChange(e.target.value)}>
              <Radio.Button value="ollama">Ollama</Radio.Button>
              <Radio.Button value="qwen">通义大模型</Radio.Button>
              <Radio.Button value="deepseek">DeepSeek大模型</Radio.Button>
            </Radio.Group>
          </Form.Item>
        </Card>

        {modelProvider === 'ollama' && (
          <Card title="Ollama设置" style={{ marginTop: 20 }}>
            <Form.Item
              name="ollamaApiUrl"
              label="Ollama API地址"
              rules={[{ required: true, message: '请输入API地址' }]}
            >
              <Input placeholder="http://localhost:11434/api" />
            </Form.Item>

            <Form.Item
              name="ollamaModel"
              label="Ollama模型"
              rules={[{ required: true, message: '请选择默认模型' }]}
              extra="如果没有可用模型，将使用系统默认模型"
            >
              <Select
                placeholder="选择模型"
                loading={modelLoading}
                notFoundContent={modelLoading ? <div style={{ textAlign: 'center', padding: '8px' }}><SyncOutlined spin /> 加载中...</div> : "没有可用模型"}
                dropdownRender={(menu) => (
                  <>
                    {menu}
                    <Divider style={{ margin: '8px 0' }} />
                    <Button
                      type="link"
                      icon={<SyncOutlined />}
                      onClick={fetchOllamaModels}
                      loading={modelLoading}
                      style={{ width: '100%', textAlign: 'center' }}
                    >
                      刷新模型列表
                    </Button>
                  </>
                )}
              >
                {ollamaModels.length > 0 ? (
                  ollamaModels.map(model => (
                    <Option key={model.name} value={model.name}>{model.name}</Option>
                  ))
                ) : (
                  <Option value="">加载默认模型中...</Option>
                )}
              </Select>
            </Form.Item>
          </Card>
        )}

        {modelProvider === 'qwen' && (
          <Card title="通义大模型设置" style={{ marginTop: 20 }}>
            <Form.Item
              name="qwenApiUrl"
              label="通义大模型 API地址"
              rules={[{ required: true, message: '请输入API地址' }]}
            >
              <Input placeholder="https://dashscope.aliyuncs.com/compatible-mode/v1/chat/completions" />
            </Form.Item>

            <Form.Item
              name="qwenApiKey"
              label="API密钥"
              rules={[{ required: true, message: '请输入API密钥' }]}
              tooltip="通义大模型的API密钥"
            >
              <Input.Password placeholder="sk-xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx" />
            </Form.Item>

            <Form.Item
              name="qwenModel"
              label="通义大模型"
              rules={[{ required: true, message: '请输入模型名称' }]}
              tooltip="输入要使用的通义大模型名称，例如：qwen-max, qwen-plus, qwen-turbo, qwen-72b-chat 等"
            >
              <Input placeholder="输入模型名称，例如：qwen-max" />
            </Form.Item>

            <Form.Item
              name="qwenEnableSearch"
              label="启用互联网搜索"
              valuePropName="checked"
              tooltip="启用后，大模型将能够搜索互联网获取最新信息"
            >
              <Switch />
            </Form.Item>
          </Card>
        )}

        {modelProvider === 'deepseek' && (
          <Card title="DeepSeek大模型设置" style={{ marginTop: 20 }}>
            <Form.Item
              name="deepseekApiUrl"
              label="DeepSeek API地址"
              rules={[{ required: true, message: '请输入API地址' }]}
            >
              <Input placeholder="https://api.deepseek.com/v1/chat/completions" />
            </Form.Item>

            <Form.Item
              name="deepseekApiKey"
              label="API密钥"
              rules={[{ required: true, message: '请输入API密钥' }]}
              tooltip="DeepSeek大模型的API密钥"
            >
              <Input.Password placeholder="sk-xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx" />
            </Form.Item>

            <Form.Item
              name="deepseekModel"
              label="DeepSeek模型"
              rules={[{ required: true, message: '请输入模型名称' }]}
              tooltip="输入要使用的DeepSeek模型名称，例如：deepseek-chat, deepseek-coder 等"
            >
              <Select placeholder="选择模型">
                <Option value="deepseek-chat">deepseek-chat</Option>
                <Option value="deepseek-coder">deepseek-coder</Option>
              </Select>
            </Form.Item>
          </Card>
        )}

        <Card title="模型参数" style={{ marginTop: 20 }}>
          <Form.Item
            name="temperature"
            label="温度 (Temperature)"
            rules={[{ required: true, message: '请输入温度值' }]}
          >
            <Select>
              <Option value={0.1}>0.1 (更确定性)</Option>
              <Option value={0.3}>0.3</Option>
              <Option value={0.5}>0.5</Option>
              <Option value={0.7}>0.7 (平衡)</Option>
              <Option value={0.9}>0.9</Option>
              <Option value={1.0}>1.0 (更创造性)</Option>
            </Select>
          </Form.Item>

          <Form.Item
            name="maxTokens"
            label="最大Token数"
            rules={[{ required: true, message: '请输入最大Token数' }]}
          >
            <Select>
              <Option value={512}>512</Option>
              <Option value={1024}>1024</Option>
              <Option value={2048}>2048</Option>
              <Option value={4096}>4096</Option>
              <Option value={8192}>8192</Option>
            </Select>
          </Form.Item>
        </Card>

        <Card title="记忆设置" style={{ marginTop: 20 }}>
          <Form.Item
            name="enableMemory"
            label="启用长期记忆"
            valuePropName="checked"
          >
            <Switch />
          </Form.Item>

          <Form.Item
            name="memoryImportanceThreshold"
            label="记忆重要性阈值"
            rules={[{ required: true, message: '请选择记忆重要性阈值' }]}
          >
            <Select>
              <Option value={0.3}>0.3 (记住更多)</Option>
              <Option value={0.5}>0.5 (平衡)</Option>
              <Option value={0.7}>0.7 (只记住重要的)</Option>
              <Option value={0.9}>0.9 (只记住非常重要的)</Option>
            </Select>
          </Form.Item>

          <Form.Item
            name="historyWindowSize"
            label="对话历史窗口大小"
            rules={[{ required: true, message: '请选择对话历史窗口大小' }]}
            tooltip="在每次对话中，大模型能够看到的历史消息数量"
          >
            <Select>
              <Option value={3}>3 条消息</Option>
              <Option value={5}>5 条消息</Option>
              <Option value={8}>8 条消息</Option>
              <Option value={10}>10 条消息</Option>
              <Option value={15}>15 条消息</Option>
              <Option value={20}>20 条消息</Option>
            </Select>
          </Form.Item>
        </Card>

        <Card title="思考模式" style={{ marginTop: 20 }}>
          <Form.Item
            name="enableDeepThinking"
            label="启用深度思考模式"
            valuePropName="checked"
          >
            <Switch />
          </Form.Item>
        </Card>

        <Card title="安全操作" style={{ marginTop: 20 }}>
          <Typography.Paragraph>
            系统支持二次确认机制，保护高风险操作的安全性。以下是操作确认示例：
          </Typography.Paragraph>
          <OperationExample />
        </Card>

        <Divider />

        <Form.Item>
          <Button
            type="primary"
            htmlType="submit"
            icon={<SaveOutlined />}
            loading={loading}
            style={{ marginRight: 8 }}
          >
            保存设置
          </Button>
          <Button
            icon={<ReloadOutlined />}
            onClick={() => form.resetFields()}
          >
            重置
          </Button>
        </Form.Item>
      </Form>
    </div>
  );
};

export default Settings;
