{"name": "sparkle-llm", "version": "1.0.0", "description": "Sparkle LLM Platform - A powerful LLM application with memory capabilities", "author": "Sparkle LLM Team", "license": "MIT", "scripts": {"start": "cd backend && npm start", "dev": "cd backend && npm run dev", "build:backend": "cd backend && npm run build", "build:frontend": "cd frontend && npm run build", "build": "npm run build:backend && npm run build:frontend", "electron:start": "electron .", "electron:build": "electron-builder build", "electron:build:linux": "electron-builder build --linux", "electron:build:win": "electron-builder build --win", "electron:build:mac": "electron-builder build --mac"}, "devDependencies": {"electron": "^36.2.1", "electron-builder": "^26.0.12"}, "main": "electron.js", "build": {"appId": "com.sparkle.llm", "productName": "Sparkle LLM", "copyright": "Copyright © 2023", "extraMetadata": {"homepage": "https://github.com/liuwenwu/sparkle-llm", "author": {"name": "Sparkle LLM Team", "email": "<EMAIL>"}}, "directories": {"output": "releases", "buildResources": "build"}, "files": ["dist/**/*", "node_modules/**/*", "backend/node_modules/**/*", "!node_modules/**/node_gyp_bins/**/*", "!backend/node_modules/**/node_gyp_bins/**/*", "package.json", "electron.js", "loading.html", "build/icons/**/*"], "extraResources": [{"from": ".env.production", "to": ".env.production"}, {"from": "frontend/dist", "to": "frontend/dist"}], "asar": false, "asarUnpack": ["node_modules/better-sqlite3/**/*", "node_modules/hnswlib-node/**/*", "node_modules/sharp/**/*", "node_modules/tslib/**/*", "node_modules/@xenova/**/*", "node_modules/onnxruntime-node/**/*", "node_modules/onnxruntime-web/**/*", "dist/**/*", "backend/node_modules/**/*"], "linux": {"target": ["AppImage", "deb"], "category": "Utility", "icon": "build/icons", "maintainer": "Sparkle LLM Team", "description": "Sparkle LLM Platform - A powerful LLM application with memory capabilities", "desktop": {"entry": {"Name": "Sparkle LLM", "Comment": "Sparkle LLM Platform", "Categories": "Utility;Development;AI;"}}}, "win": {"target": ["nsis", "portable"], "icon": "build/icons/icon.ico"}, "nsis": {"oneClick": false, "allowToChangeInstallationDirectory": true, "createDesktopShortcut": true, "createStartMenuShortcut": true, "shortcutName": "Sparkle LLM"}, "mac": {"target": ["dmg"], "category": "public.app-category.developer-tools", "icon": "build/icons/icon.icns"}, "dmg": {"contents": [{"x": 130, "y": 220}, {"x": 410, "y": 220, "type": "link", "path": "/Applications"}]}, "publish": null}, "dependencies": {"@xenova/transformers": "^2.17.2", "tslib": "^2.8.1"}}