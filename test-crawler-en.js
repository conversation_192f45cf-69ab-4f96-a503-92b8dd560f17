const axios = require('axios');

async function testWebSearchSummarize() {
  try {
    const response = await axios.post('http://localhost:3001/api/tools/execute', {
      tool_name: 'web_search_summarize',
      input: {
        query: 'JavaScript programming'
      }
    });
    
    console.log('搜索并总结结果:');
    console.log(JSON.stringify(response.data, null, 2));
    return response.data;
  } catch (error) {
    console.error('测试搜索并总结工具失败:', error.response ? error.response.data : error.message);
  }
}

async function testWebSearchLinks() {
  try {
    const response = await axios.post('http://localhost:3001/api/tools/execute', {
      tool_name: 'web_search_links',
      input: {
        query: 'JavaScript programming'
      }
    });
    
    console.log('搜索并返回链接结果:');
    console.log(JSON.stringify(response.data, null, 2));
    return response.data;
  } catch (error) {
    console.error('测试搜索并返回链接工具失败:', error.response ? error.response.data : error.message);
  }
}

async function testWebFetchContent() {
  try {
    const response = await axios.post('http://localhost:3001/api/tools/execute', {
      tool_name: 'web_fetch_content',
      input: {
        url: 'https://cn.bing.com'
      }
    });
    
    console.log('获取网页内容结果:');
    console.log(JSON.stringify(response.data, null, 2).substring(0, 500) + '...');
    return response.data;
  } catch (error) {
    console.error('测试获取网页内容工具失败:', error.response ? error.response.data : error.message);
  }
}

async function runTests() {
  console.log('开始测试爬虫工具...');
  
  // 测试搜索并总结
  await testWebSearchSummarize();
  
  console.log('\n-----------------------------------\n');
  
  // 测试搜索并返回链接
  await testWebSearchLinks();
  
  console.log('\n-----------------------------------\n');
  
  // 测试获取网页内容
  await testWebFetchContent();
  
  console.log('\n测试完成');
}

runTests();
