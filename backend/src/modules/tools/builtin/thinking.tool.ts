/**
 * 深度思考工具
 * 当大模型判断需要进行深度思考时，可以调用此工具启动深度思考模式
 */
import { toolManager } from '../tool.manager';
import { ToolCategory } from '../tools.types';

/**
 * 注册深度思考工具
 */
export function registerThinkingTools(): void {
  // 深度思考工具
  toolManager.registerTool({
    name: 'deep_thinking',
    description: '启动深度思考模式，用于处理复杂问题。当遇到需要多步骤分析、复杂推理的问题时，应该调用此工具。',
    input_schema: {
      type: 'object',
      properties: {
        question: {
          type: 'string',
          description: '需要深度思考的问题或任务描述',
        },
        reason: {
          type: 'string',
          description: '为什么需要深度思考的原因，例如：问题复杂、需要多步骤分析等',
        },
        use_tools: {
          type: 'boolean',
          description: '在深度思考过程中是否需要使用工具',
          default: true,
        },
      },
      required: ['question', 'reason'],
    },
    handler: async (input: { question: string; reason: string; use_tools?: boolean }) => {
      const { question, reason, use_tools = true } = input;

      try {
        console.log('深度思考工具被调用:', { question, reason, use_tools });

        // 导入工具管理器
        const { toolManager } = require('../tool.manager');

        // 检查深度思考层级限制
        const currentDepth = toolManager.getThinkingDepth();
        if (currentDepth >= 2) {
          console.log(`深度思考层级已达到最大值 (${currentDepth})，拒绝启动新的深度思考`);
          return {
            success: false,
            message: '已达到最大深度思考层级限制（2层），无法启动新的深度思考',
            reason: reason,
            question: question,
            current_depth: currentDepth
          };
        }

        // 增加深度思考层级
        toolManager.incrementThinkingDepth();

        // 获取全局的Socket实例（如果存在）
        const globalSocket = (global as any).activeSocket;

        if (!globalSocket) {
          // 如果没有Socket连接，减少深度思考层级并返回提示信息
          toolManager.decrementThinkingDepth();
          return {
            success: false,
            message: '深度思考模式需要WebSocket连接支持，请通过聊天界面使用此功能',
            reason: reason,
            question: question
          };
        }

        // 导入必要的服务
        const { modelService } = require('../../model');
        const { conversationService } = require('../../conversation');
        const { settingService } = require('../../settings');
        const { SocketEventType } = require('../../socket/socket.types');
        const { v4: uuidv4 } = require('uuid');

        // 获取或创建活动对话
        let activeConversation = await conversationService.getActiveConversation();
        if (!activeConversation) {
          activeConversation = await conversationService.createConversation('深度思考对话');
        }

        // 发送深度思考启动通知
        globalSocket.emit(SocketEventType.CHAT_MESSAGE, {
          content: `🧠 **深度思考模式已启动**\n\n**原因**: ${reason}\n\n**问题**: ${question}\n\n正在生成思考步骤计划...`,
          sender: 'ai',
          timestamp: Date.now()
        });

        // 发送思考开始事件
        globalSocket.emit(SocketEventType.CHAT_THINKING_START);

        try {
          // 1. 生成思考步骤计划
          console.log('1. 生成思考步骤计划');
          const historyWindow = settingService.getHistoryWindowSize();
          const plan = await modelService.generateThinkingPlan(question, {
            useTools: use_tools,
            historyWindow,
            conversationId: activeConversation.id
          });

          if (!plan || !plan.steps || plan.steps.length === 0) {
            console.error('无法生成有效的思考步骤计划');

            // 减少深度思考层级
            toolManager.decrementThinkingDepth();

            globalSocket.emit(SocketEventType.ERROR, {
              message: '深度思考失败',
              details: '无法生成有效的思考步骤计划'
            });

            return {
              success: false,
              message: '无法生成有效的思考步骤计划',
              reason: reason,
              question: question
            };
          }

          console.log('思考步骤计划:', JSON.stringify(plan, null, 2));

          // 2. 创建初始思考步骤
          const initialSteps = plan.steps.map((step: any) => ({
            id: uuidv4(),
            content: step.title,
            status: 'pending',
            objective: step.objective,
            operations_log: ['步骤已创建，等待执行'],
            current_operation: '等待执行'
          }));

          // 添加最终回答步骤
          initialSteps.push({
            id: uuidv4(),
            content: '生成最终回答',
            status: 'pending',
            operations_log: ['步骤已创建，等待执行'],
            current_operation: '等待执行'
          });

          // 发送思考步骤
          globalSocket.emit(SocketEventType.THINKING_STEPS, initialSteps);

          // 启动深度思考处理器（如果存在）
          const thinkingHandler = (global as any).thinkingHandler;
          if (thinkingHandler) {
            // 初始化思考会话
            thinkingHandler.thinkingSession = {
              message: question,
              useTools: use_tools,
              activeStepId: null,
              waitingForUserInput: false,
              conversationId: activeConversation.id,
              plan,
              stepResults: [],
              currentSteps: [...initialSteps]
            };

            // 开始执行思考步骤
            await thinkingHandler.executeNextStep(globalSocket);
          }

          return {
            success: true,
            message: '深度思考模式已启动，正在执行思考步骤',
            reason: reason,
            question: question,
            steps_count: plan.steps.length,
            use_tools: use_tools
          };

        } catch (error) {
          console.error('深度思考失败:', error);

          // 减少深度思考层级
          toolManager.decrementThinkingDepth();

          globalSocket.emit(SocketEventType.ERROR, {
            message: '深度思考失败',
            details: error instanceof Error ? error.message : String(error)
          });

          return {
            success: false,
            message: '深度思考启动失败',
            error: error instanceof Error ? error.message : String(error),
            reason: reason,
            question: question
          };
        }

      } catch (error) {
        console.error('深度思考工具执行错误:', error);

        // 导入工具管理器并减少深度思考层级
        const { toolManager } = require('../tool.manager');
        toolManager.decrementThinkingDepth();

        return {
          success: false,
          message: '深度思考工具执行失败',
          error: error instanceof Error ? error.message : String(error),
          reason: reason,
          question: question
        };
      }
    },
    requires_auth: false,
    category: ToolCategory.THINKING,
  });
}
