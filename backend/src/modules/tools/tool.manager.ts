/**
 * 工具管理器
 */
import { Tool } from './interfaces/tool.interface';
import { ToolCall, ToolCallResult } from './tools.types';

/**
 * 工具管理类
 * 负责注册、获取和执行工具
 */
export class ToolManager {
  /**
   * 工具映射表
   */
  private tools: Map<string, Tool> = new Map();

  /**
   * 当前深度思考层级
   */
  private thinkingDepth: number = 0;

  /**
   * 注册工具
   * @param tool 工具对象
   */
  registerTool(tool: Tool): void {
    if (this.tools.has(tool.name)) {
      console.warn(`工具 ${tool.name} 已存在，将被覆盖`);
    }
    this.tools.set(tool.name, tool);
    console.log(`工具 ${tool.name} 已注册`);
  }

  /**
   * 获取工具
   * @param name 工具名称
   * @returns 工具对象或undefined
   */
  getTool(name: string): Tool | undefined {
    return this.tools.get(name);
  }

  /**
   * 获取所有工具
   * @returns 工具数组
   */
  getAllTools(): Tool[] {
    return Array.from(this.tools.values());
  }

  /**
   * 获取工具描述（用于大模型API）- 传统格式
   * @returns 工具描述数组
   */
  getToolDescriptions(): any[] {
    return Array.from(this.tools.values()).map(tool => ({
      name: tool.name,
      description: tool.description,
      input_schema: tool.input_schema,
    }));
  }

  /**
   * 获取OpenAI兼容的函数调用格式工具描述
   * @param excludeThinking 是否排除深度思考工具
   * @returns OpenAI兼容的函数调用格式工具描述
   */
  getOpenAIFunctionTools(excludeThinking: boolean = false): any[] {
    let toolsToUse = Array.from(this.tools.values());

    // 如果需要排除深度思考工具，或者当前深度思考层级大于等于1，则过滤掉深度思考工具
    if (excludeThinking || this.thinkingDepth >= 1) {
      toolsToUse = toolsToUse.filter(tool => tool.name !== 'deep_thinking');
    }

    return toolsToUse.map(tool => ({
      type: 'function',
      function: {
        name: tool.name,
        description: tool.description,
        parameters: {
          ...tool.input_schema,
          // 确保有type字段
          type: tool.input_schema.type || 'object'
        }
      }
    }));
  }

  /**
   * 执行工具
   * @param name 工具名称
   * @param input 工具输入参数
   * @returns 工具调用结果
   */
  async executeTool(name: string, input: any): Promise<ToolCallResult> {
    const tool = this.tools.get(name);
    if (!tool) {
      return {
        tool_name: name,
        tool_input: input,
        tool_output: null,
        error: `工具 ${name} 不存在`,
      };
    }

    try {
      const output = await tool.handler(input);
      return {
        tool_name: name,
        tool_input: input,
        tool_output: output,
      };
    } catch (error) {
      console.error(`执行工具 ${name} 错误:`, error);
      return {
        tool_name: name,
        tool_input: input,
        tool_output: null,
        error: `执行工具错误: ${error instanceof Error ? error.message : String(error)}`,
      };
    }
  }

  /**
   * 批量执行工具
   * @param toolCalls 工具调用数组
   * @returns 工具调用结果数组
   */
  async executeTools(toolCalls: ToolCall[]): Promise<ToolCallResult[]> {
    const results: ToolCallResult[] = [];

    for (const call of toolCalls) {
      const result = await this.executeTool(call.name, call.input);
      results.push(result);
    }

    return results;
  }

  /**
   * 增加深度思考层级
   */
  incrementThinkingDepth(): void {
    this.thinkingDepth++;
    console.log(`深度思考层级增加到: ${this.thinkingDepth}`);
  }

  /**
   * 减少深度思考层级
   */
  decrementThinkingDepth(): void {
    if (this.thinkingDepth > 0) {
      this.thinkingDepth--;
      console.log(`深度思考层级减少到: ${this.thinkingDepth}`);
    }
  }

  /**
   * 重置深度思考层级
   */
  resetThinkingDepth(): void {
    this.thinkingDepth = 0;
    console.log('深度思考层级已重置');
  }

  /**
   * 获取当前深度思考层级
   * @returns 当前深度思考层级
   */
  getThinkingDepth(): number {
    return this.thinkingDepth;
  }
}

// 创建工具管理器实例
export const toolManager = new ToolManager();
