/**
 * 记忆组织服务
 * 负责记忆的整理、冲突检测和处理
 */

import { db } from '../../config/database';
import { modelService } from '../model';
import { IMemoryOrganizationService } from './interfaces/memory.interface';
import { MemoryConflictAnalysis } from './memory.types';
import { MemoryService } from './memory.service';

/**
 * 记忆组织服务类
 */
export class MemoryOrganizationService implements IMemoryOrganizationService {
  private memoryService: MemoryService;
  // 记忆整理计数器
  private memoryCounter: number = 0;
  // 整理阈值
  private readonly ORGANIZATION_THRESHOLD: number = 20;

  /**
   * 构造函数
   * @param memoryService 记忆服务实例
   */
  constructor(memoryService: MemoryService) {
    this.memoryService = memoryService;
  }

  /**
   * 记忆整理服务
   * 当新记忆创建时调用此函数，增加计数器
   * 当计数器达到阈值时，触发记忆整理
   */
  async incrementMemoryCounter(): Promise<void> {
    this.memoryCounter++;
    console.log(`记忆计数器: ${this.memoryCounter}/${this.ORGANIZATION_THRESHOLD}`);

    if (this.memoryCounter >= this.ORGANIZATION_THRESHOLD) {
      console.log('触发记忆整理...');
      await this.organizeMemories();
      // 重置计数器
      this.memoryCounter = 0;
    }
  }

  /**
   * 记忆整理主函数
   * 获取所有记忆，调用大模型进行分析，处理冲突记忆
   */
  async organizeMemories(): Promise<void> {
    try {
      // 获取所有记忆
      const memories = await this.memoryService.getAllMemories();

      if (memories.length === 0) {
        console.log('没有记忆需要整理');
        return;
      }

      console.log(`开始整理 ${memories.length} 条记忆...`);

      // 将记忆转换为适合大模型处理的格式
      const memoriesForAnalysis = memories.map(memory => ({
        id: memory.id,
        keywords: memory.keywords,
        content: memory.content,
        timestamp: memory.timestamp,
        created_at: memory.created_at,
        memory_type: memory.memory_type,
        memory_subtype: memory.memory_subtype,
        importance_level: memory.importance_level
      }));

      // 调用大模型分析记忆冲突
      const conflictAnalysis = await this.analyzeMemoryConflicts(memoriesForAnalysis);

      // 处理分析结果
      if (conflictAnalysis.conflicts && conflictAnalysis.conflicts.length > 0) {
        console.log(`发现 ${conflictAnalysis.conflicts.length} 组冲突记忆`);

        // 处理每组冲突
        for (const conflict of conflictAnalysis.conflicts) {
          await this.handleMemoryConflict(conflict);
        }

        console.log('记忆整理完成');
      } else {
        console.log('未发现冲突记忆');
      }
    } catch (error) {
      console.error('记忆整理失败:', error);
    }
  }

  /**
   * 调用大模型分析记忆冲突
   * @param memories 记忆列表
   * @returns 冲突分析结果
   */
  private async analyzeMemoryConflicts(memories: any[]): Promise<MemoryConflictAnalysis> {
    try {
      // 导入提示词服务
      const { promptService } = require('../../modules/model/prompts');

      // 获取记忆整理提示词
      const prompt = promptService.getMemoryConflictAnalysisPrompt(memories);

      // 调用大模型
      const response = await modelService.generateText(prompt);

      // 解析JSON响应
      try {
        // 导入文本处理工具
        const { parseJsonFromModelResponse } = require('../../utils/text-processor');

        // 使用通用的JSON解析函数
        const result = parseJsonFromModelResponse(response, {
          logPrefix: 'MemoryConflict',
          defaultValue: { conflicts: [] }
        });

        return result;
      } catch (parseError) {
        console.error('解析大模型返回的JSON失败:', parseError);
        return { conflicts: [] };
      }
    } catch (error) {
      console.error('调用大模型分析记忆冲突失败:', error);
      return { conflicts: [] };
    }
  }

  /**
   * 处理记忆冲突
   * 保留指定的记忆，删除其他冲突记忆
   * @param conflict 冲突信息
   */
  private async handleMemoryConflict(conflict: {
    description: string;
    conflicting_ids: number[];
    keep_id: number;
    reason: string;
  }): Promise<void> {
    try {
      const { conflicting_ids, keep_id, description, reason } = conflict;

      // 记录冲突处理日志
      console.log(`处理冲突: ${description}`);
      console.log(`保留记忆ID: ${keep_id}, 原因: ${reason}`);

      // 获取要保留的记忆详情
      const keepMemory = db.prepare('SELECT * FROM memories WHERE id = ?').get(keep_id) as {
        id: number;
        content: string;
        created_at: number;
      };
      if (!keepMemory) {
        console.error(`要保留的记忆ID ${keep_id} 不存在`);
        return;
      }

      // 记录整理操作到审计日志
      const timestamp = Date.now();
      const auditLogId = `memory-org-${timestamp}-${Math.random().toString(36).substring(2, 10)}`;

      db.prepare(`
        INSERT INTO audit_logs (
          id, user_id, operation_id, operation_type, risk_level,
          success, timestamp, details
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)
      `).run(
        auditLogId,
        'system',
        `memory-org-${timestamp}`,
        'memory_organization',
        'low',
        1,
        timestamp,
        JSON.stringify({
          description,
          reason,
          keep_memory: {
            id: keepMemory.id,
            content: keepMemory.content,
            created_at: new Date(keepMemory.created_at).toISOString()
          }
        })
      );

      // 删除冲突记忆（除了要保留的）
      for (const memoryId of conflicting_ids) {
        if (memoryId !== keep_id) {
          console.log(`删除冲突记忆ID: ${memoryId}`);
          await this.memoryService.deleteMemory(memoryId);
        }
      }
    } catch (error) {
      console.error('处理记忆冲突失败:', error);
    }
  }

  /**
   * 手动触发记忆整理
   * @returns 操作结果
   */
  async triggerMemoryOrganization(): Promise<{
    success: boolean;
    message: string;
  }> {
    try {
      await this.organizeMemories();
      return {
        success: true,
        message: '记忆整理已完成'
      };
    } catch (error) {
      return {
        success: false,
        message: `记忆整理失败: ${error instanceof Error ? error.message : String(error)}`
      };
    }
  }

  /**
   * 创建记忆关系
   * @param memoryId 记忆ID
   * @param relatedMemoryId 关联记忆ID
   */
  async createMemoryRelation(memoryId: number, relatedMemoryId: number): Promise<void> {
    try {
      const timestamp = Date.now();

      // 检查记忆是否存在
      const memory = db.prepare('SELECT id FROM memories WHERE id = ?').get(memoryId);
      const relatedMemory = db.prepare('SELECT id FROM memories WHERE id = ?').get(relatedMemoryId);

      if (!memory || !relatedMemory) {
        throw new Error('记忆不存在');
      }

      // 检查关系是否已存在
      const existingRelation = db.prepare(
        'SELECT * FROM memory_relations WHERE memory_id = ? AND related_memory_id = ?'
      ).get(memoryId, relatedMemoryId);

      if (existingRelation) {
        console.log(`记忆关系已存在: ${memoryId} -> ${relatedMemoryId}`);
        return;
      }

      // 创建记忆关系
      db.prepare(`
        INSERT INTO memory_relations (memory_id, related_memory_id, relation_strength, created_at)
        VALUES (?, ?, ?, ?)
      `).run(memoryId, relatedMemoryId, 1.0, timestamp);

      console.log(`创建记忆关系成功: ${memoryId} -> ${relatedMemoryId}`);
    } catch (error) {
      console.error('创建记忆关系失败:', error);
      throw error;
    }
  }

  /**
   * 删除记忆关系
   * @param memoryId 记忆ID
   * @param relatedMemoryId 关联记忆ID
   */
  async deleteMemoryRelation(memoryId: number, relatedMemoryId: number): Promise<void> {
    try {
      // 删除记忆关系
      db.prepare(
        'DELETE FROM memory_relations WHERE memory_id = ? AND related_memory_id = ?'
      ).run(memoryId, relatedMemoryId);

      console.log(`删除记忆关系成功: ${memoryId} -> ${relatedMemoryId}`);
    } catch (error) {
      console.error('删除记忆关系失败:', error);
      throw error;
    }
  }

  /**
   * 获取关联记忆
   * @param memoryId 记忆ID
   * @returns 关联记忆列表
   */
  async getRelatedMemories(memoryId: number): Promise<any[]> {
    try {
      // 获取关联记忆
      const relatedMemories = db.prepare(`
        SELECT m.* FROM memories m
        JOIN memory_relations mr ON m.id = mr.related_memory_id
        WHERE mr.memory_id = ?
      `).all(memoryId);

      return relatedMemories;
    } catch (error) {
      console.error('获取关联记忆失败:', error);
      return [];
    }
  }
}
