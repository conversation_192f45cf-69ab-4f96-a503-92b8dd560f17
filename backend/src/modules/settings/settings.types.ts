/**
 * 设置类型定义
 */

/**
 * 设置项类型
 */
export interface Setting {
  key: string;
  value: string;
  updated_at: number;
}

/**
 * 模型提供商类型
 */
export type ModelProviderType = 'ollama' | 'qwen' | 'deepseek';

/**
 * 模型配置类型
 */
export interface ModelConfig {
  provider: string;
  temperature: number;
  maxTokens: number;
  ollama?: {
    apiUrl: string;
    model: string;
  };
  qwen?: {
    apiUrl: string;
    apiKey: string;
    model: string;
    enableSearch?: boolean;
  };
  deepseek?: {
    apiUrl: string;
    apiKey: string;
    model: string;
  };
}
