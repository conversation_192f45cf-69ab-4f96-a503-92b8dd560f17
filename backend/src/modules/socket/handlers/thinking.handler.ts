/**
 * 思考处理器
 */
import { Server as SocketIOServer, Socket } from 'socket.io';
import {
  ThinkingStep,
  ThinkingRequest,
  ThinkingUserAnswerRequest,
  SocketEventType
} from '../socket.types';
import { SocketHandler } from '../socket.types';
import { modelService } from '../../model';
import { conversationService } from '../../conversation';
import { memoryService } from '../../memory';
import { settingService } from '../../settings';
import { v4 as uuidv4 } from 'uuid';

// 添加一个全局的Socket引用，用于在模型服务中发送操作日志更新
let activeSocket: Socket | null = null;

/**
 * 思考处理器
 */
export class ThinkingHandler implements SocketHandler {
  // 存储当前思考会话的状态
  private thinkingSession: {
    message: string;
    useTools: boolean;
    activeStepId: string | null;
    waitingForUserInput: boolean;
    conversationId: number | null;
    plan: any;
    stepResults: any[];
    currentSteps: ThinkingStep[];
  } | null = null;

  /**
   * 更新步骤的操作日志并实时发送到前端
   * @param socket Socket实例
   * @param stepId 步骤ID
   * @param operationLog 操作日志
   */
  public updateStepOperationLog(socket: Socket, stepId: string, operationLog: string): void {
    if (!this.thinkingSession) {
      console.error('没有活动的思考会话，无法更新操作日志');
      return;
    }

    // 找到对应的步骤
    const stepIndex = this.thinkingSession.currentSteps.findIndex(step => step.id === stepId);
    if (stepIndex === -1) {
      console.error('找不到指定的思考步骤:', stepId);
      return;
    }

    // 更新操作日志
    if (!this.thinkingSession.currentSteps[stepIndex].operations_log) {
      this.thinkingSession.currentSteps[stepIndex].operations_log = [];
    }

    // 添加新的操作日志
    this.thinkingSession.currentSteps[stepIndex].operations_log.push(
      `${operationLog} (${new Date().toLocaleTimeString()})`
    );

    // 更新当前操作
    this.thinkingSession.currentSteps[stepIndex].current_operation = operationLog;

    // 发送操作日志更新事件
    socket.emit(SocketEventType.THINKING_STEP_OPERATION, {
      stepId,
      operationLog: `${operationLog} (${new Date().toLocaleTimeString()})`,
      operations_log: this.thinkingSession.currentSteps[stepIndex].operations_log,
      current_operation: operationLog
    });
  }

  /**
   * 处理思考事件
   * @param socket Socket实例
   * @param io SocketIO服务器实例
   */
  handle(socket: Socket, io: SocketIOServer): void {
    // 保存当前socket引用，以便在模型服务中使用
    activeSocket = socket;

    // 导出思考处理器实例，以便在深度思考工具中使用
    (global as any).thinkingHandler = this;
    (global as any).activeSocket = socket;

    // 导出更新操作日志的方法，以便在模型服务中使用
    (global as any).updateThinkingStepOperationLog = (stepId: string, operationLog: string) => {
      if (activeSocket) {
        this.updateStepOperationLog(activeSocket, stepId, operationLog);
      }
    };
    socket.on(SocketEventType.THINKING_START, async (request: ThinkingRequest) => {
      try {
        console.log('开始深度思考:', request);
        const { message, useTools } = request;

        // 获取或创建活动对话
        let activeConversation = await conversationService.getActiveConversation();
        if (!activeConversation) {
          // 创建新对话，使用默认标题，后续会更新
          activeConversation = await conversationService.createConversation('新对话');
        }

        // 保存用户消息到数据库
        await conversationService.addMessageToConversation(
          activeConversation.id,
          message,
          'user'
        );

        // 发送用户消息
        socket.emit(SocketEventType.CHAT_MESSAGE, {
          content: message,
          sender: 'user',
          timestamp: Date.now()
        });

        // 发送思考开始事件
        socket.emit(SocketEventType.CHAT_THINKING_START);

        try {
          // 1. 生成思考步骤计划
          console.log('1. 生成思考步骤计划');
          const historyWindow = settingService.getHistoryWindowSize();
          const plan = await modelService.generateThinkingPlan(message, {
            useTools,
            historyWindow,
            conversationId: activeConversation.id
          });

          if (!plan || !plan.steps || plan.steps.length === 0) {
            console.error('无法生成有效的思考步骤计划');

            // 发送错误消息
            socket.emit(SocketEventType.ERROR, {
              message: '深度思考失败',
              details: '无法生成有效的思考步骤计划'
            });

            // 发送一个默认回答
            const defaultAnswer = '抱歉，我无法为这个问题生成思考步骤。请尝试提供更多信息或换一种方式提问。';

            await conversationService.addMessageToConversation(
              activeConversation.id,
              defaultAnswer,
              'ai'
            );

            socket.emit(SocketEventType.CHAT_MESSAGE, {
              content: defaultAnswer,
              sender: 'ai',
              timestamp: Date.now()
            });

            return;
          }

          console.log('思考步骤计划:', JSON.stringify(plan, null, 2));

          // 2. 创建初始思考步骤
          const initialSteps: ThinkingStep[] = plan.steps.map(step => ({
            id: uuidv4(),
            content: step.title,
            status: 'pending',
            objective: step.objective, // 添加步骤的详细目标
            operations_log: ['步骤已创建，等待执行'], // 初始化操作日志
            current_operation: '等待执行'
          }));

          // 添加最终回答步骤
          initialSteps.push({
            id: uuidv4(),
            content: '生成最终回答',
            status: 'pending',
            operations_log: ['步骤已创建，等待执行'], // 初始化操作日志
            current_operation: '等待执行'
          });

          // 发送用户引导信息
          socket.emit(SocketEventType.CHAT_MESSAGE, {
            content: `
**深度思考模式已启动**

我将把您的问题分解为多个思考步骤，逐步分析并得出结论。请注意：
- 每个步骤的结论将直接用于生成最终答案
- 如果我在某个步骤中向您提问，您的回答将帮助我完善该步骤的结论
- 您可以点击展开每个步骤查看详细内容
- 最终答案将基于所有步骤的结论综合生成

现在开始分析您的问题...
            `,
            sender: 'ai',
            timestamp: Date.now()
          });

          // 发送初始思考步骤
          socket.emit(SocketEventType.THINKING_STEPS, initialSteps);

          // 初始化思考会话
          this.thinkingSession = {
            message,
            useTools,
            activeStepId: null,
            waitingForUserInput: false,
            conversationId: activeConversation.id,
            plan,
            stepResults: [],
            currentSteps: [...initialSteps]
          };

          // 3. 逐步执行思考步骤
          console.log('2. 开始执行思考步骤');
          await this.executeNextStep(socket);
        } catch (error) {
          console.error('深度思考失败:', error);

          // 发送错误消息
          socket.emit(SocketEventType.ERROR, {
            message: '深度思考失败',
            details: error instanceof Error ? error.message : String(error)
          });

          // 发送一个默认回答
          const errorAnswer = '抱歉，思考过程中出现了问题，无法生成有效回答。';

          if (this.thinkingSession && this.thinkingSession.conversationId) {
            await conversationService.addMessageToConversation(
              this.thinkingSession.conversationId,
              errorAnswer,
              'ai'
            );
          }

          socket.emit(SocketEventType.CHAT_MESSAGE, {
            content: errorAnswer,
            sender: 'ai',
            timestamp: Date.now()
          });

          // 重置思考会话
          this.thinkingSession = null;
        }
      } catch (error) {
        console.error('处理思考请求失败:', error);

        // 发送错误消息
        socket.emit(SocketEventType.ERROR, {
          message: '处理思考请求失败',
          details: error instanceof Error ? error.message : String(error)
        });

        // 重置思考会话
        this.thinkingSession = null;
      }
    });

    // 处理用户对思考步骤的回答
    socket.on(SocketEventType.THINKING_USER_ANSWER, async (request: ThinkingUserAnswerRequest) => {
      try {
        console.log('收到用户对思考步骤的回答:', request);
        const { stepId, answer } = request;

        // 检查是否有活动的思考会话
        if (!this.thinkingSession || !this.thinkingSession.waitingForUserInput || this.thinkingSession.activeStepId !== stepId) {
          console.error('没有等待用户输入的思考步骤或步骤ID不匹配');

          socket.emit(SocketEventType.ERROR, {
            message: '处理用户回答失败',
            details: '没有等待用户输入的思考步骤或步骤ID不匹配'
          });

          return;
        }

        // 更新步骤状态
        const stepIndex = this.thinkingSession.currentSteps.findIndex(step => step.id === stepId);
        if (stepIndex === -1) {
          console.error('找不到指定的思考步骤:', stepId);
          return;
        }

        // 更新步骤状态为进行中
        this.thinkingSession.currentSteps[stepIndex].status = 'process';
        this.thinkingSession.currentSteps[stepIndex].user_answer = answer;

        // 发送更新后的思考步骤
        socket.emit(SocketEventType.THINKING_STEPS, this.thinkingSession.currentSteps);

        // 继续执行当前步骤
        await this.continueStepWithUserAnswer(socket, stepIndex, answer);
      } catch (error) {
        console.error('处理用户回答失败:', error);

        // 发送错误消息
        socket.emit(SocketEventType.ERROR, {
          message: '处理用户回答失败',
          details: error instanceof Error ? error.message : String(error)
        });
      }
    });
  }

  /**
   * 执行下一个思考步骤
   * @param socket Socket实例
   */
  private async executeNextStep(socket: Socket): Promise<void> {
    if (!this.thinkingSession) {
      console.error('没有活动的思考会话');
      return;
    }

    const { message, useTools, plan, stepResults, currentSteps } = this.thinkingSession;

    // 找到下一个待执行的步骤
    const nextStepIndex = currentSteps.findIndex(step => step.status === 'pending');
    if (nextStepIndex === -1 || nextStepIndex >= plan.steps.length) {
      // 所有步骤已完成或到达最终回答步骤
      if (nextStepIndex === currentSteps.length - 1) {
        // 执行最终回答步骤
        await this.generateFinalAnswer(socket);
      }
      return;
    }

    // 更新当前步骤状态为进行中
    currentSteps[nextStepIndex].status = 'process';
    currentSteps[nextStepIndex].current_operation = '正在执行步骤';

    // 添加操作日志
    if (!currentSteps[nextStepIndex].operations_log) {
      currentSteps[nextStepIndex].operations_log = [];
    }
    currentSteps[nextStepIndex].operations_log.push(`开始执行步骤 (${new Date().toLocaleTimeString()})`);

    this.thinkingSession.activeStepId = currentSteps[nextStepIndex].id;
    socket.emit(SocketEventType.THINKING_STEPS, currentSteps);

    try {
      // 执行思考步骤
      const historyWindow = settingService.getHistoryWindowSize();
      // 获取当前步骤的详细信息
      const currentStepInfo = this.thinkingSession.plan.steps[nextStepIndex];

      const stepResult = await modelService.executeThinkingStep(
        message,
        currentSteps[nextStepIndex].content,
        stepResults,
        useTools,
        null, // 没有用户回答
        null, // 第一次执行步骤，没有历史
        {
          historyWindow,
          conversationId: this.thinkingSession.conversationId,
          currentStep: currentStepInfo, // 传递当前步骤的完整信息，包括详细目标
          stepId: currentSteps[nextStepIndex].id // 传递步骤ID，用于实时更新操作日志
        }
      );

      if (stepResult) {
        // 检查是否需要用户输入
        if (stepResult.needs_user_input && stepResult.question) {
          console.log('步骤需要用户输入:', stepResult.question);

          // 更新操作日志
          const operationsLog = currentSteps[nextStepIndex].operations_log || [];
          operationsLog.push(`需要用户输入: ${stepResult.question} (${new Date().toLocaleTimeString()})`);

          // 如果有工具调用，记录工具调用信息
          if (stepResult.tool_calls && stepResult.tool_calls.length > 0) {
            operationsLog.push(`调用了 ${stepResult.tool_calls.length} 个工具 (${new Date().toLocaleTimeString()})`);

            // 记录每个工具调用的详情
            stepResult.tool_calls.forEach((toolCall, idx) => {
              const toolResult = stepResult.tool_results ? stepResult.tool_results[idx] : null;
              if (toolResult && toolResult.error) {
                operationsLog.push(`工具 ${toolCall.name} 调用失败: ${toolResult.error} (${new Date().toLocaleTimeString()})`);
              } else {
                operationsLog.push(`工具 ${toolCall.name} 调用成功 (${new Date().toLocaleTimeString()})`);
              }
            });
          }

          // 更新步骤状态为等待用户输入
          currentSteps[nextStepIndex] = {
            ...currentSteps[nextStepIndex],
            status: 'waiting_for_user',
            result: stepResult.content,
            question: stepResult.question,
            has_tools: stepResult.tool_calls && stepResult.tool_calls.length > 0,
            tool_calls: stepResult.tool_calls, // 保存工具调用信息
            tool_results: stepResult.tool_results, // 保存工具调用结果
            operations_log: operationsLog,
            current_operation: '等待用户输入'
          };

          // 更新会话状态
          this.thinkingSession.waitingForUserInput = true;

          // 发送更新后的思考步骤
          socket.emit(SocketEventType.THINKING_STEPS, currentSteps);

          // 等待用户回答，不继续执行下一步
          return;
        }

        // 添加步骤结果
        stepResults.push(stepResult);

        // 构建步骤结果内容
        let resultContent = `${stepResult.content}\n\n`;
        if (stepResult.conclusion) {
          resultContent += `结论: ${stepResult.conclusion}\n\n`;
        }

        // 如果有图片链接，添加图片信息
        if (stepResult.images && stepResult.images.length > 0) {
          resultContent += '相关图片:\n';
          stepResult.images.forEach((imageUrl: string) => {
            resultContent += `![图片](${imageUrl})\n`;
          });
          resultContent += '\n';
        }

        // 如果有工具调用，添加工具调用信息
        if (stepResult.tool_calls && stepResult.tool_calls.length > 0) {
          resultContent += '工具调用:\n';
          stepResult.tool_calls.forEach((toolCall, idx) => {
            const toolResult = stepResult.tool_results ? stepResult.tool_results[idx] : null;
            resultContent += `- 工具: ${toolCall.name}\n`;
            resultContent += `- 输入: ${JSON.stringify(toolCall.input, null, 2)}\n`;
            if (toolResult) {
              resultContent += `- 输出: ${JSON.stringify(toolResult.tool_output, null, 2)}\n`;
            }
            if (toolResult && toolResult.error) {
              resultContent += `- 错误: ${toolResult.error}\n`;
            }
            resultContent += '\n';
          });
        }

        // 更新操作日志
        // 如果stepResult中包含operations_log，则使用它，否则使用当前步骤的操作日志
        let operationsLog = [];
        if (stepResult.operations_log && stepResult.operations_log.length > 0) {
          // 使用stepResult中的操作日志
          operationsLog = stepResult.operations_log;
        } else {
          // 使用当前步骤的操作日志
          operationsLog = currentSteps[nextStepIndex].operations_log || [];

          // 记录工具调用
          if (stepResult.tool_calls && stepResult.tool_calls.length > 0) {
            operationsLog.push(`调用了 ${stepResult.tool_calls.length} 个工具 (${new Date().toLocaleTimeString()})`);

            // 记录每个工具调用的详情
            stepResult.tool_calls.forEach((toolCall, idx) => {
              const toolResult = stepResult.tool_results ? stepResult.tool_results[idx] : null;
              if (toolResult && toolResult.error) {
                operationsLog.push(`工具 ${toolCall.name} 调用失败: ${toolResult.error} (${new Date().toLocaleTimeString()})`);
              } else {
                operationsLog.push(`工具 ${toolCall.name} 调用成功 (${new Date().toLocaleTimeString()})`);
              }
            });
          }
        }

        // 记录步骤完成
        operationsLog.push(`步骤执行完成 (${new Date().toLocaleTimeString()})`);

        // 更新步骤状态为已完成
        currentSteps[nextStepIndex] = {
          ...currentSteps[nextStepIndex],
          status: 'completed',
          result: resultContent,
          has_tools: stepResult.tool_calls && stepResult.tool_calls.length > 0,
          tool_calls: stepResult.tool_calls, // 保存工具调用信息
          tool_results: stepResult.tool_results, // 保存工具调用结果
          images: stepResult.images, // 传递图片链接数组
          operations_log: operationsLog, // 更新操作日志
          current_operation: '步骤已完成'
        };

        // 发送更新后的思考步骤
        socket.emit(SocketEventType.THINKING_STEPS, currentSteps);

        // 重置活动步骤ID
        this.thinkingSession.activeStepId = null;

        // 执行下一个步骤
        await this.executeNextStep(socket);
      } else {
        console.error(`步骤 ${nextStepIndex + 1} 执行失败`);

        // 更新操作日志
        const operationsLog = currentSteps[nextStepIndex].operations_log || [];
        operationsLog.push(`步骤执行失败 (${new Date().toLocaleTimeString()})`);

        // 更新步骤状态为错误
        currentSteps[nextStepIndex] = {
          ...currentSteps[nextStepIndex],
          status: 'error',
          result: '执行步骤时出现错误',
          operations_log: operationsLog,
          current_operation: '执行失败'
        };

        socket.emit(SocketEventType.THINKING_STEPS, currentSteps);

        // 重置活动步骤ID
        this.thinkingSession.activeStepId = null;

        // 尝试执行下一个步骤
        await this.executeNextStep(socket);
      }
    } catch (error) {
      console.error(`执行步骤 ${nextStepIndex + 1} 失败:`, error);

      // 更新操作日志
      const operationsLog = currentSteps[nextStepIndex].operations_log || [];
      const errorMessage = error instanceof Error ? error.message : String(error);
      operationsLog.push(`步骤执行异常: ${errorMessage} (${new Date().toLocaleTimeString()})`);

      // 更新步骤状态为错误
      currentSteps[nextStepIndex] = {
        ...currentSteps[nextStepIndex],
        status: 'error',
        result: `执行步骤时出现错误: ${errorMessage}`,
        operations_log: operationsLog,
        current_operation: '执行异常'
      };

      socket.emit(SocketEventType.THINKING_STEPS, currentSteps);

      // 重置活动步骤ID
      this.thinkingSession.activeStepId = null;

      // 尝试执行下一个步骤
      await this.executeNextStep(socket);
    }
  }

  /**
   * 使用用户回答继续执行当前步骤
   * @param socket Socket实例
   * @param stepIndex 步骤索引
   * @param userAnswer 用户回答
   */
  private async continueStepWithUserAnswer(socket: Socket, stepIndex: number, userAnswer: string): Promise<void> {
    if (!this.thinkingSession) {
      console.error('没有活动的思考会话');
      return;
    }

    const { message, useTools, stepResults, currentSteps } = this.thinkingSession;

    try {
      // 检查用户是否表示不想继续回答问题
      const skipKeywords = ['不要问我了', '下一步', '继续', '不需要', '跳过', '不想回答'];
      const shouldSkipQuestions = skipKeywords.some(keyword => userAnswer.includes(keyword));

      if (shouldSkipQuestions) {
        console.log('检测到用户希望跳过问题，直接完成当前步骤');

        // 构建一个自动生成的结论
        const autoConclusion = `根据用户要求，此步骤自动完成。用户回答: "${userAnswer}"`;

        // 构建步骤结果内容
        let resultContent = `${currentSteps[stepIndex].result || ''}\n\n用户回答: ${userAnswer}\n\n系统: 用户选择跳过此步骤的问题，直接完成当前步骤。\n\n`;
        resultContent += `结论: ${autoConclusion}\n\n`;

        // 更新步骤状态为已完成
        currentSteps[stepIndex] = {
          ...currentSteps[stepIndex],
          status: 'completed',
          result: resultContent
        };

        // 创建一个简化的步骤结果对象
        const simplifiedStepResult = {
          title: currentSteps[stepIndex].content,
          content: `用户选择跳过此步骤的问题，直接完成。`,
          conclusion: autoConclusion
        };

        // 添加步骤结果
        stepResults.push(simplifiedStepResult);

        // 发送更新后的思考步骤
        socket.emit(SocketEventType.THINKING_STEPS, currentSteps);

        // 重置会话状态
        this.thinkingSession.waitingForUserInput = false;
        this.thinkingSession.activeStepId = null;

        // 执行下一个步骤
        await this.executeNextStep(socket);
        return;
      }

      // 正常执行思考步骤，传入用户回答
      const historyWindow = settingService.getHistoryWindowSize();
      // 传递当前步骤的完整历史
      // 获取当前步骤的详细信息
      const currentStepInfo = this.thinkingSession.plan.steps[stepIndex];

      const stepResult = await modelService.executeThinkingStep(
        message,
        currentSteps[stepIndex].content,
        stepResults,
        useTools,
        userAnswer, // 传入用户回答
        currentSteps[stepIndex].result, // 传入步骤的完整历史
        {
          historyWindow,
          conversationId: this.thinkingSession.conversationId,
          currentStep: currentStepInfo, // 传递当前步骤的完整信息，包括详细目标
          stepId: currentSteps[stepIndex].id // 传递步骤ID，用于实时更新操作日志
        }
      );

      if (stepResult) {
        // 检查是否仍然需要用户输入
        if (stepResult.needs_user_input && stepResult.question) {
          console.log('步骤仍然需要用户输入:', stepResult.question);

          // 更新操作日志
          const operationsLog = currentSteps[stepIndex].operations_log || [];
          operationsLog.push(`收到用户回答: ${userAnswer} (${new Date().toLocaleTimeString()})`);
          operationsLog.push(`需要继续用户输入: ${stepResult.question} (${new Date().toLocaleTimeString()})`);

          // 如果有工具调用，记录工具调用信息
          if (stepResult.tool_calls && stepResult.tool_calls.length > 0) {
            operationsLog.push(`调用了 ${stepResult.tool_calls.length} 个工具 (${new Date().toLocaleTimeString()})`);

            // 记录每个工具调用的详情
            stepResult.tool_calls.forEach((toolCall, idx) => {
              const toolResult = stepResult.tool_results ? stepResult.tool_results[idx] : null;
              if (toolResult && toolResult.error) {
                operationsLog.push(`工具 ${toolCall.name} 调用失败: ${toolResult.error} (${new Date().toLocaleTimeString()})`);
              } else {
                operationsLog.push(`工具 ${toolCall.name} 调用成功 (${new Date().toLocaleTimeString()})`);
              }
            });
          }

          // 更新步骤状态为等待用户输入
          currentSteps[stepIndex] = {
            ...currentSteps[stepIndex],
            status: 'waiting_for_user',
            result: `${currentSteps[stepIndex].result || ''}\n\n用户回答: ${userAnswer}\n\n继续思考: ${stepResult.content}`,
            question: stepResult.question,
            has_tools: stepResult.tool_calls && stepResult.tool_calls.length > 0,
            tool_calls: stepResult.tool_calls, // 保存工具调用信息
            tool_results: stepResult.tool_results, // 保存工具调用结果
            operations_log: operationsLog,
            current_operation: '等待用户继续输入'
          };

          // 更新会话状态
          this.thinkingSession.waitingForUserInput = true;

          // 发送更新后的思考步骤
          socket.emit(SocketEventType.THINKING_STEPS, currentSteps);

          // 等待用户回答，不继续执行下一步
          return;
        }

        // 添加步骤结果
        stepResults.push(stepResult);

        // 构建步骤结果内容
        let resultContent = `${currentSteps[stepIndex].result || ''}\n\n用户回答: ${userAnswer}\n\n继续思考: ${stepResult.content}\n\n`;
        if (stepResult.conclusion) {
          resultContent += `结论: ${stepResult.conclusion}\n\n`;
        }

        // 如果有图片链接，添加图片信息
        if (stepResult.images && stepResult.images.length > 0) {
          resultContent += '相关图片:\n';
          stepResult.images.forEach((imageUrl: string) => {
            resultContent += `![图片](${imageUrl})\n`;
          });
          resultContent += '\n';
        }

        // 如果有工具调用，添加工具调用信息
        if (stepResult.tool_calls && stepResult.tool_calls.length > 0) {
          resultContent += '工具调用:\n';
          stepResult.tool_calls.forEach((toolCall, idx) => {
            const toolResult = stepResult.tool_results ? stepResult.tool_results[idx] : null;
            resultContent += `- 工具: ${toolCall.name}\n`;
            resultContent += `- 输入: ${JSON.stringify(toolCall.input, null, 2)}\n`;
            if (toolResult) {
              resultContent += `- 输出: ${JSON.stringify(toolResult.tool_output, null, 2)}\n`;
            }
            if (toolResult && toolResult.error) {
              resultContent += `- 错误: ${toolResult.error}\n`;
            }
            resultContent += '\n';
          });
        }

        // 更新操作日志
        // 如果stepResult中包含operations_log，则使用它，否则使用当前步骤的操作日志
        let operationsLog = [];
        if (stepResult.operations_log && stepResult.operations_log.length > 0) {
          // 使用stepResult中的操作日志
          operationsLog = stepResult.operations_log;
          // 确保记录了用户回答
          if (!operationsLog.some(log => log.includes(`收到用户回答: ${userAnswer}`))) {
            operationsLog.push(`收到用户回答: ${userAnswer} (${new Date().toLocaleTimeString()})`);
          }
        } else {
          // 使用当前步骤的操作日志
          operationsLog = currentSteps[stepIndex].operations_log || [];
          operationsLog.push(`收到用户回答: ${userAnswer} (${new Date().toLocaleTimeString()})`);

          // 记录工具调用
          if (stepResult.tool_calls && stepResult.tool_calls.length > 0) {
            operationsLog.push(`调用了 ${stepResult.tool_calls.length} 个工具 (${new Date().toLocaleTimeString()})`);

            // 记录每个工具调用的详情
            stepResult.tool_calls.forEach((toolCall, idx) => {
              const toolResult = stepResult.tool_results ? stepResult.tool_results[idx] : null;
              if (toolResult && toolResult.error) {
                operationsLog.push(`工具 ${toolCall.name} 调用失败: ${toolResult.error} (${new Date().toLocaleTimeString()})`);
              } else {
                operationsLog.push(`工具 ${toolCall.name} 调用成功 (${new Date().toLocaleTimeString()})`);
              }
            });
          }
        }

        // 记录步骤完成
        operationsLog.push(`步骤执行完成 (${new Date().toLocaleTimeString()})`);

        // 更新步骤状态为已完成
        currentSteps[stepIndex] = {
          ...currentSteps[stepIndex],
          status: 'completed',
          result: resultContent,
          has_tools: stepResult.tool_calls && stepResult.tool_calls.length > 0,
          tool_calls: stepResult.tool_calls, // 保存工具调用信息
          tool_results: stepResult.tool_results, // 保存工具调用结果
          images: stepResult.images, // 传递图片链接数组
          operations_log: operationsLog,
          current_operation: '步骤已完成'
        };

        // 发送更新后的思考步骤
        socket.emit(SocketEventType.THINKING_STEPS, currentSteps);

        // 重置会话状态
        this.thinkingSession.waitingForUserInput = false;
        this.thinkingSession.activeStepId = null;

        // 执行下一个步骤
        await this.executeNextStep(socket);
      } else {
        console.error(`使用用户回答继续执行步骤 ${stepIndex + 1} 失败`);

        // 更新操作日志
        const operationsLog = currentSteps[stepIndex].operations_log || [];
        operationsLog.push(`收到用户回答: ${userAnswer} (${new Date().toLocaleTimeString()})`);
        operationsLog.push(`处理用户回答时出现错误 (${new Date().toLocaleTimeString()})`);

        // 更新步骤状态为错误
        currentSteps[stepIndex] = {
          ...currentSteps[stepIndex],
          status: 'error',
          result: `${currentSteps[stepIndex].result || ''}\n\n用户回答: ${userAnswer}\n\n处理用户回答时出现错误`,
          operations_log: operationsLog,
          current_operation: '处理用户回答失败'
        };

        socket.emit(SocketEventType.THINKING_STEPS, currentSteps);

        // 重置会话状态
        this.thinkingSession.waitingForUserInput = false;
        this.thinkingSession.activeStepId = null;

        // 尝试执行下一个步骤
        await this.executeNextStep(socket);
      }
    } catch (error) {
      console.error(`使用用户回答继续执行步骤 ${stepIndex + 1} 失败:`, error);

      // 更新操作日志
      const operationsLog = currentSteps[stepIndex].operations_log || [];
      const errorMessage = error instanceof Error ? error.message : String(error);
      operationsLog.push(`收到用户回答: ${userAnswer} (${new Date().toLocaleTimeString()})`);
      operationsLog.push(`处理用户回答时出现异常: ${errorMessage} (${new Date().toLocaleTimeString()})`);

      // 更新步骤状态为错误
      currentSteps[stepIndex] = {
        ...currentSteps[stepIndex],
        status: 'error',
        result: `${currentSteps[stepIndex].result || ''}\n\n用户回答: ${userAnswer}\n\n处理用户回答时出现错误: ${errorMessage}`,
        operations_log: operationsLog,
        current_operation: '处理用户回答异常'
      };

      socket.emit(SocketEventType.THINKING_STEPS, currentSteps);

      // 重置会话状态
      this.thinkingSession.waitingForUserInput = false;
      this.thinkingSession.activeStepId = null;

      // 尝试执行下一个步骤
      await this.executeNextStep(socket);
    }
  }

  /**
   * 生成最终答案
   * @param socket Socket实例
   */
  private async generateFinalAnswer(socket: Socket): Promise<void> {
    if (!this.thinkingSession) {
      console.error('没有活动的思考会话');
      return;
    }

    const { message, stepResults, currentSteps } = this.thinkingSession;

    // 更新最终回答步骤状态为进行中
    const finalStepIndex = currentSteps.length - 1;

    // 更新操作日志
    if (!currentSteps[finalStepIndex].operations_log) {
      currentSteps[finalStepIndex].operations_log = [];
    }
    currentSteps[finalStepIndex].operations_log.push(`开始生成最终答案 (${new Date().toLocaleTimeString()})`);

    currentSteps[finalStepIndex].status = 'process';
    currentSteps[finalStepIndex].current_operation = '正在生成最终答案';
    socket.emit(SocketEventType.THINKING_STEPS, currentSteps);

    try {
      // 生成最终答案
      const historyWindow = settingService.getHistoryWindowSize();
      const finalAnswer = await modelService.generateFinalAnswer(message, stepResults, {
        historyWindow,
        conversationId: this.thinkingSession.conversationId
      });

      // 更新操作日志
      const operationsLog = currentSteps[finalStepIndex].operations_log || [];
      operationsLog.push(`最终答案生成完成 (${new Date().toLocaleTimeString()})`);

      // 更新最终回答步骤状态为已完成
      currentSteps[finalStepIndex] = {
        ...currentSteps[finalStepIndex],
        status: 'completed',
        result: finalAnswer,
        operations_log: operationsLog,
        current_operation: '最终答案已生成'
      };

      socket.emit(SocketEventType.THINKING_STEPS, currentSteps);

      console.log('深度思考AI回答:', finalAnswer);

      // 保存AI回答到数据库
      if (this.thinkingSession.conversationId) {
        await conversationService.addMessageToConversation(
          this.thinkingSession.conversationId,
          finalAnswer,
          'ai'
        );

        // 更新对话时间
        await conversationService.setActiveConversation(this.thinkingSession.conversationId);

        // 发送对话更新通知
        const updatedConversation = await conversationService.getConversationById(this.thinkingSession.conversationId);
        if (updatedConversation) {
          socket.emit(SocketEventType.CONVERSATION_UPDATED, updatedConversation);
        }
      }

      // 发送思考结束事件
      socket.emit(SocketEventType.CHAT_THINKING_END);

      // 发送AI回答消息
      socket.emit(SocketEventType.CHAT_MESSAGE, {
        content: finalAnswer,
        sender: 'ai',
        timestamp: Date.now()
      });

      // 重置思考会话
      this.thinkingSession = null;
    } catch (error) {
      console.error('生成最终答案失败:', error);

      // 更新操作日志
      const operationsLog = currentSteps[finalStepIndex].operations_log || [];
      const errorMessage = error instanceof Error ? error.message : String(error);
      operationsLog.push(`生成最终答案失败: ${errorMessage} (${new Date().toLocaleTimeString()})`);

      // 更新最终回答步骤状态为错误
      currentSteps[finalStepIndex] = {
        ...currentSteps[finalStepIndex],
        status: 'error',
        result: `生成最终答案时出现错误: ${errorMessage}`,
        operations_log: operationsLog,
        current_operation: '最终答案生成失败'
      };

      socket.emit(SocketEventType.THINKING_STEPS, currentSteps);

      // 发送一个默认回答
      const errorAnswer = '抱歉，思考过程中出现了问题，无法生成有效回答。';

      if (this.thinkingSession.conversationId) {
        await conversationService.addMessageToConversation(
          this.thinkingSession.conversationId,
          errorAnswer,
          'ai'
        );
      }

      // 发送思考结束事件
      socket.emit(SocketEventType.CHAT_THINKING_END);

      socket.emit(SocketEventType.CHAT_MESSAGE, {
        content: errorAnswer,
        sender: 'ai',
        timestamp: Date.now()
      });

      // 重置思考会话
      this.thinkingSession = null;
    }
  }
}
