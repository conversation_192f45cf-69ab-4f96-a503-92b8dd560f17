import { ModelService as IModelService } from './interfaces/model-service.interface';
import { ModelProvider, ModelProviderType, Message } from './interfaces/model-provider.interface';
import { OllamaProvider } from './providers/ollama.provider';
import { QwenProvider } from './providers/qwen.provider';
import { DeepSeekProvider } from './providers/deepseek.provider';
import { ModelConfig } from './model.config';
import { toolManager } from '../tools';
import { memoryService } from '../memory';
import { conversationService } from '../conversation';
import { settingService } from '../settings';
import { promptService } from './prompts';
import { SocketEventType } from '../socket/socket.types';

/**
 * 模型服务
 * 提供与大语言模型交互的服务
 */
export class ModelService implements IModelService {
  private ollamaProvider: ModelProvider;
  private qwenProvider: ModelProvider;
  private deepseekProvider: ModelProvider;

  /**
   * 构造函数
   */
  constructor() {
    this.ollamaProvider = new OllamaProvider();
    this.qwenProvider = new QwenProvider();
    this.deepseekProvider = new DeepSeekProvider();
  }

  /**
   * 获取当前模型提供商类型
   * @returns 模型提供商类型
   */
  getModelProvider(): ModelProviderType {
    return ModelConfig.getModelProvider();
  }

  /**
   * 获取当前模型提供商实例
   * @returns 模型提供商实例
   */
  getModelProviderInstance(): ModelProvider {
    const provider = this.getModelProvider();

    if (provider === 'ollama') {
      return this.ollamaProvider;
    } else if (provider === 'qwen') {
      return this.qwenProvider;
    } else if (provider === 'deepseek') {
      return this.deepseekProvider;
    }

    // 默认返回Ollama提供商
    return this.ollamaProvider;
  }

  /**
   * 获取默认模型名称
   * @returns 默认模型名称
   */
  private getDefaultModel(): string {
    return ModelConfig.getCurrentModel();
  }

  /**
   * 使用消息数组生成文本
   * @param messages 消息数组
   * @param options 生成选项
   * @returns 生成的文本
   */
  async generateTextWithMessages(messages: Message[], options: any = {}): Promise<string> {
    const modelProvider = this.getModelProviderInstance();
    const providerType = this.getModelProvider();
    let model = options.model || this.getDefaultModel();

    // 确保使用正确的模型名称格式
    // 如果当前提供商是通义，但模型名称是Ollama格式（包含冒号），则使用默认的通义模型名称
    if (providerType === 'qwen' && model.includes(':')) {
      console.log(`当前提供商是通义，但检测到Ollama格式的模型名称: ${model}，将使用默认通义模型: qwen-max`);
      model = 'qwen-max';
    }
    // 如果当前提供商是DeepSeek，但模型名称是Ollama格式（包含冒号），则使用默认的DeepSeek模型名称
    else if (providerType === 'deepseek' && model.includes(':')) {
      console.log(`当前提供商是DeepSeek，但检测到Ollama格式的模型名称: ${model}，将使用默认DeepSeek模型: deepseek-chat`);
      model = 'deepseek-chat';
    }
    // 如果当前提供商是Ollama，但模型名称是通义或DeepSeek格式（不包含冒号），则使用默认的Ollama模型名称
    else if (providerType === 'ollama' && !model.includes(':')) {
      const defaultOllamaModel = settingService.getSetting('ollama_model') || 'qwen3:1.7b';
      console.log(`当前提供商是Ollama，但检测到非Ollama格式的模型名称: ${model}，将使用默认Ollama模型: ${defaultOllamaModel}`);
      model = defaultOllamaModel;
    }

    try {
      // 创建消息数组的副本，避免修改原始数组
      let messagesCopy = [...messages];

      // 如果指定了对话ID，则添加对话历史
      if (options.conversationId) {
        const { messages: conversationMessages } = await conversationService.getConversationById(options.conversationId);

        // 获取历史窗口大小
        const historyWindowSize = settingService.getHistoryWindowSize();

        // 获取最近的消息
        const recentMessages = conversationMessages.slice(-historyWindowSize);

        // 将对话历史转换为消息数组格式
        const historyMessagesArray: Message[] = recentMessages.map(msg => ({
          role: msg.sender === 'user' ? 'user' : 'assistant',
          content: msg.content
        }));

        // 将历史消息添加到消息数组前面
        messagesCopy = [...historyMessagesArray, ...messagesCopy];
      }

      // 如果启用了记忆，则添加相关记忆
      if (options.enableMemory !== false) {
        // 获取用户最后一条消息的内容用于查找相关记忆
        // 从后向前查找用户消息
        const userMessages = messagesCopy.filter(msg => msg.role === 'user');
        const lastUserMessage = userMessages.length > 0 ? userMessages[userMessages.length - 1].content : '';
        const memories = await memoryService.findRelatedMemories(lastUserMessage);

        if (memories.length > 0) {
          // 构建记忆提示
          let memoryContent = '以下是一些你应该记住的信息：\n\n';
          for (const memory of memories) {
            memoryContent += `- ${memory.content}\n`;
          }

          // 添加系统消息到消息数组前面
          const systemMessage: Message = {
            role: 'system',
            content: memoryContent
          };

          // 检查是否已有系统消息
          const hasSystemMessage = messagesCopy.some(msg => msg.role === 'system');

          if (hasSystemMessage) {
            // 如果已有系统消息，则更新第一条系统消息
            const systemIndex = messagesCopy.findIndex(msg => msg.role === 'system');
            messagesCopy[systemIndex].content += '\n\n' + memoryContent;
          } else {
            // 如果没有系统消息，则添加到消息数组前面
            messagesCopy = [systemMessage, ...messagesCopy];
          }
        }
      }

      // 调用模型提供商生成文本
      return await modelProvider.generateTextWithMessages(messagesCopy, {
        ...options,
        model
      });
    } catch (error) {
      console.error('生成文本错误:', error);
      throw error;
    }
  }

  /**
   * 生成文本（兼容旧接口）
   * @param prompt 提示词
   * @param options 生成选项
   * @returns 生成的文本
   */
  async generateText(prompt: string, options: any = {}): Promise<string> {
    // 将提示词转换为消息数组格式
    const messages: Message[] = [
      {
        role: 'user',
        content: prompt
      }
    ];

    // 获取系统提示词，除非明确指定跳过
    if (!options.skipSystemPrompt) {
      const systemPrompt = promptService.getSystemPrompt();
      if (systemPrompt) {
        messages.unshift({
          role: 'system',
          content: systemPrompt
        });
      }
    }

    return this.generateTextWithMessages(messages, options);
  }

  /**
   * 生成思考步骤计划
   * @param question 问题
   * @param options 生成选项
   * @returns 思考步骤计划
   */
  async generateThinkingPlan(question: string, options: any = {}): Promise<any> {
    // 获取默认模型
    const model = options.model || this.getDefaultModel();

    try {
      // 获取深度思考步骤规划提示词
      const prompt = promptService.getThinkingPlanPrompt(question);

      const response = await this.generateText(prompt, {
        ...options,
        model,
        temperature: 0.2,
        max_tokens: 2048
      });

      // 尝试解析JSON
      try {
        // 导入文本处理工具
        const { parseJsonFromModelResponse } = require('../../utils/text-processor');

        // 使用通用的JSON解析函数
        return parseJsonFromModelResponse(response, {
          logPrefix: 'ThinkingPlan',
          defaultValue: null
        });
      } catch (error) {
        console.error('解析思考步骤计划JSON错误:', error);
        return null;
      }
    } catch (error) {
      console.error('生成思考步骤计划错误:', error);
      throw error;
    }
  }

  /**
   * 执行单个思考步骤
   * @param question 原始问题
   * @param stepTitle 步骤标题
   * @param previousSteps 之前步骤的结果
   * @param useTools 是否使用工具
   * @param userAnswer 用户对上一个问题的回答
   * @param stepHistory 当前步骤的完整对话历史
   * @param options 生成选项，包括stepId用于实时更新操作日志
   * @returns 步骤执行结果
   */
  async executeThinkingStep(
    question: string,
    stepTitle: string,
    previousSteps: any[] = [],
    useTools: boolean = false,
    userAnswer: string | null = null,
    stepHistory: string | null = null, // 添加步骤历史参数
    options: any = {}
  ): Promise<any> {
    // 获取默认模型
    const model = options.model || this.getDefaultModel();
    // 如果需要使用工具，将工具描述传递给options
    if (useTools) {
      options.tools = toolManager.getToolDescriptions();
    }

    try {
      // 这些变量已经在promptService.getThinkingStepPrompt中处理，不需要在这里构建
      console.log('执行思考步骤，禁用对话历史、记忆和系统提示词，减少token消耗');

      // 获取思考步骤执行提示词
      const prompt = promptService.getThinkingStepPrompt(
        question,
        stepTitle,
        previousSteps,
        userAnswer,
        stepHistory,
        options
      );

      const response = await this.generateText(prompt, {
        ...options,
        model,
        temperature: 0.3,
        max_tokens: 4096,
        conversationId: null, // 禁用对话历史
        enableMemory: false,  // 禁用记忆
        skipSystemPrompt: true // 禁用系统提示词
      });

      // 尝试解析JSON
      try {
        // 导入文本处理工具
        const { parseJsonFromModelResponse } = require('../../utils/text-processor');

        // 使用通用的JSON解析函数
        const stepResult = parseJsonFromModelResponse(response, {
          logPrefix: 'ThinkingStep',
          defaultValue: null
        });

        if (stepResult) {
          // 检查是否需要向用户提问
          if (stepResult.ask_user && stepResult.question) {
            console.log('步骤需要向用户提问:', stepResult.question);
            return {
              ...stepResult,
              needs_user_input: true
            };
          }

          // 检查是否需要使用工具
          if (useTools && stepResult.use_tools && stepResult.tool_calls && stepResult.tool_calls.length > 0) {
            console.log('检测到思考步骤中的工具调用请求，执行工具...');
            // 传递stepId用于实时更新操作日志
            return await this.executeToolsInThinkingStep(question, stepTitle, previousSteps, stepResult, stepHistory, options);
          }

          // 如果有工具调用，执行工具
          if (useTools && stepResult.tool_calls && stepResult.tool_calls.length > 0) {
            console.log('检测到思考步骤中的工具调用，执行工具...');

            // 执行所有工具调用
            const toolResults = await Promise.all(
              stepResult.tool_calls.map(async (toolCall: any) => {
                return await toolManager.executeTool(toolCall.name, toolCall.input);
              })
            );

            // 将工具调用结果添加到步骤结果中
            stepResult.tool_results = toolResults;

            // 使用工具结果继续思考
            const toolResultsText = toolResults.map((result, index) => {
              const toolCall = stepResult.tool_calls[index];
              return `工具: ${toolCall.name}\n输入: ${JSON.stringify(toolCall.input)}\n输出: ${JSON.stringify(result.tool_output)}`;
            }).join('\n\n');

            // 获取思考步骤工具结果处理提示词
            const continuePrompt = promptService.getThinkingStepContinuePrompt(
              question,
              stepTitle,
              stepResult.content,
              toolResultsText,
              stepHistory,
              options
            );

            const continueResponse = await this.generateText(continuePrompt, {
              ...options,
              model,
              temperature: 0.3,
              max_tokens: 4096,
              conversationId: null, // 禁用对话历史
              enableMemory: false,  // 禁用记忆
              skipSystemPrompt: true // 禁用系统提示词
            });

            // 解析继续思考的结果
            const continueResult = parseJsonFromModelResponse(continueResponse, {
              logPrefix: 'ThinkingStepContinue',
              defaultValue: null
            });

            if (continueResult) {
              // 检查是否需要向用户提问
              if (continueResult.ask_user && continueResult.question) {
                console.log('工具调用后步骤需要向用户提问:', continueResult.question);
                return {
                  title: stepResult.title,
                  content: stepResult.content + '\n\n工具调用结果分析:\n' + continueResult.content,
                  tool_calls: stepResult.tool_calls,
                  tool_results: stepResult.tool_results,
                  needs_user_input: true,
                  question: continueResult.question
                };
              }

              // 更新步骤结果
              stepResult.content += '\n\n工具调用结果分析:\n' + continueResult.content;
              stepResult.conclusion = continueResult.conclusion;
            }
          }

          // 记录步骤结果中的图片链接，用于调试
          if (stepResult.images && stepResult.images.length > 0) {
            console.log(`步骤 "${stepTitle}" 包含图片:`, stepResult.images);
            console.log(`步骤结论:`, stepResult.conclusion);
          }

          return stepResult;
        }
        return null;
      } catch (error) {
        console.error('解析思考步骤JSON错误:', error);
        return null;
      }
    } catch (error) {
      console.error('执行思考步骤错误:', error);
      throw error;
    }
  }

  /**
   * 生成最终答案
   * @param question 原始问题
   * @param steps 所有步骤的结果
   * @param options 生成选项
   * @returns 最终答案
   */
  async generateFinalAnswer(question: string, steps: any[], options: any = {}): Promise<string> {
    const model = options.model || this.getDefaultModel();

    try {
      // 记录步骤中的图片链接，用于调试
      console.log('最终答案生成 - 步骤中的图片链接:', steps.map(step => step.images || []));
      console.log('最终答案生成 - 启用对话历史和记忆，确保回答与整个对话上下文一致');

      // 获取最终答案生成提示词
      const prompt = promptService.getFinalAnswerPrompt(question, steps);

      return await this.generateText(prompt, {
        ...options,
        model,
        temperature: 0.3,
        max_tokens: 4096
      });
    } catch (error) {
      console.error('生成最终答案错误:', error);
      throw error;
    }
  }

  /**
   * 深度思考
   * @param question 问题
   * @param options 生成选项
   * @returns 思考结果
   */
  async deepThinking(question: string, options: any = {}): Promise<any> {
    try {
      console.log('开始深度思考流程...');

      // 1. 生成思考步骤计划
      console.log('1. 生成思考步骤计划');
      const plan = await this.generateThinkingPlan(question, options);

      if (!plan || !plan.steps || plan.steps.length === 0) {
        console.error('无法生成有效的思考步骤计划');
        return null;
      }

      console.log('思考步骤计划:', JSON.stringify(plan, null, 2));

      // 2. 逐步执行思考步骤
      console.log('2. 开始执行思考步骤');
      const stepResults = [];
      const useTools = options.useTools || false;

      for (let i = 0; i < plan.steps.length; i++) {
        const step = plan.steps[i];
        console.log(`执行步骤 ${i + 1}/${plan.steps.length}: ${step.title}`);

        const stepResult = await this.executeThinkingStep(
          question,
          step.title,
          stepResults,
          useTools,
          null, // 没有用户回答
          null, // 第一次执行步骤，没有历史
          {
            ...options,
            currentStep: step // 传递当前步骤的完整信息，包括详细目标
          }
        );

        if (stepResult) {
          stepResults.push(stepResult);
        } else {
          console.error(`步骤 ${i + 1} 执行失败`);
        }
      }

      // 3. 生成最终答案
      console.log('3. 生成最终答案');
      const finalAnswer = await this.generateFinalAnswer(question, stepResults, options);

      // 4. 构建完整的思考结果
      return {
        steps: stepResults,
        final_answer: finalAnswer
      };
    } catch (error) {
      console.error('深度思考错误:', error);
      throw error;
    }
  }

  /**
   * 带工具的深度思考
   * @param question 问题
   * @param options 生成选项
   * @returns 思考结果
   */
  async deepThinkingWithTools(question: string, options: any = {}): Promise<any> {
    // 获取模型名称
    const model = options.model || this.getDefaultModel();
    const tools = toolManager.getToolDescriptions();

    try {
      console.log('开始带工具的深度思考...');

      // 获取带工具的深度思考提示词
      const prompt = promptService.getDeepThinkingWithToolsPrompt(question, tools);

      // 生成回答
      const response = await this.generateText(prompt, {
        ...options,
        model,
        temperature: 0.2,
        max_tokens: 4096
      });

      // 尝试解析JSON
      try {
        // 导入文本处理工具
        const { removeThinkTags, fixJsonString } = require('../../utils/text-processor');

        // 移除<think>标签，避免JSON解析错误
        const cleanedResponse = removeThinkTags(response);

        // 提取JSON部分
        const jsonMatch = cleanedResponse.match(/\{[\s\S]*\}/);
        if (jsonMatch) {
          // 修复JSON字符串中的特殊字符
          const fixedJsonStr = fixJsonString(jsonMatch[0]);

          // 解析修复后的JSON
          return JSON.parse(fixedJsonStr);
        }
        return null;
      } catch (error) {
        console.error('解析深度思考JSON错误:', error);
        // 在catch块中不能访问try块中的变量，所以不能直接使用jsonMatch
        return null;
      }
    } catch (error) {
      console.error('带工具的深度思考错误:', error);
      throw error;
    }
  }

  /**
   * 生成记忆
   * @param context 上下文
   * @param options 生成选项
   * @returns 生成的记忆数据
   */
  async generateMemory(context: string, options: any = {}): Promise<any> {
    const modelProvider = this.getModelProviderInstance();
    const model = options.model || this.getDefaultModel();

    try {
      console.log('开始分析对话内容，判断是否生成记忆...');
      const memoryData = await modelProvider.generateMemory(context, {
        ...options,
        model
      });

      if (memoryData) {
        console.log('生成记忆成功:', memoryData.keywords);
      } else {
        console.log('对话内容不包含重要记忆，不生成记忆');
      }

      return memoryData;
    } catch (error) {
      console.error('生成记忆错误:', error);
      throw error;
    }
  }

  /**
   * 使用工具生成回答（使用消息数组）
   * @param messages 消息数组
   * @param options 生成选项
   * @returns 生成结果
   */
  async generateWithToolsUsingMessages(messages: Message[], options: any = {}): Promise<any> {
    const modelProvider = this.getModelProviderInstance();
    const providerType = this.getModelProvider();
    let model = options.model || this.getDefaultModel();

    // 确保使用正确的模型名称格式
    // 如果当前提供商是通义，但模型名称是Ollama格式（包含冒号），则使用默认的通义模型名称
    if (providerType === 'qwen' && model.includes(':')) {
      console.log(`工具调用: 当前提供商是通义，但检测到Ollama格式的模型名称: ${model}，将使用默认通义模型: qwen-max`);
      model = 'qwen-max';
    }
    // 如果当前提供商是Ollama，但模型名称是通义格式（不包含冒号），则使用默认的Ollama模型名称
    else if (providerType === 'ollama' && !model.includes(':')) {
      const defaultOllamaModel = settingService.getSetting('ollama_model') || 'qwen3:1.7b';
      console.log(`工具调用: 当前提供商是Ollama，但检测到通义格式的模型名称: ${model}，将使用默认Ollama模型: ${defaultOllamaModel}`);
      model = defaultOllamaModel;
    }

    // 所有模型都使用OpenAI兼容的函数调用格式
    const tools = toolManager.getOpenAIFunctionTools();

    try {
      console.log('开始使用工具生成回答...');

      // 创建消息数组的副本，避免修改原始数组
      let messagesCopy = [...messages];

      // 如果指定了对话ID，则添加对话历史
      if (options.conversationId) {
        const { messages: conversationMessages } = await conversationService.getConversationById(options.conversationId);

        // 获取历史窗口大小
        const historyWindowSize = settingService.getHistoryWindowSize();

        // 获取最近的消息
        const recentMessages = conversationMessages.slice(-historyWindowSize);

        // 将对话历史转换为消息数组格式
        const historyMessagesArray: Message[] = recentMessages.map(msg => ({
          role: msg.sender === 'user' ? 'user' : 'assistant',
          content: msg.content
        }));

        // 将历史消息添加到消息数组前面
        messagesCopy = [...historyMessagesArray, ...messagesCopy];
      }

      // 如果启用了记忆，则添加相关记忆
      if (options.enableMemory !== false) {
        // 获取用户最后一条消息的内容用于查找相关记忆
        const userMessages = messagesCopy.filter(msg => msg.role === 'user');
        const lastUserMessage = userMessages.length > 0 ? userMessages[userMessages.length - 1].content : '';
        const memories = await memoryService.findRelatedMemories(lastUserMessage);

        if (memories.length > 0) {
          // 构建记忆提示
          const memoryContent = promptService.getMemoriesPrompt(memories);

          // 添加系统消息到消息数组前面
          const systemMessage: Message = {
            role: 'system',
            content: memoryContent
          };

          // 检查是否已有系统消息
          const hasSystemMessage = messagesCopy.some(msg => msg.role === 'system');

          if (hasSystemMessage) {
            // 如果已有系统消息，则更新第一条系统消息
            const systemIndex = messagesCopy.findIndex(msg => msg.role === 'system');
            messagesCopy[systemIndex].content += '\n\n' + memoryContent;
          } else {
            // 如果没有系统消息，则添加到消息数组前面
            messagesCopy = [systemMessage, ...messagesCopy];
          }
        }
      }

      // 调用模型提供商生成工具调用
      const result = await modelProvider.generateWithToolsUsingMessages(messagesCopy, tools, {
        ...options,
        model
      });

      // 如果有工具调用，执行工具并获取结果
      if (result.toolCalls && result.toolCalls.length > 0) {
        console.log('检测到工具调用，执行工具...');

        // 如果提供了socket实例，发送工具调用中的状态
        if (options.socket) {
          const toolCallingMessage: any = {
            content: result.content || "我正在使用工具来回答您的问题...",
            sender: 'ai',
            timestamp: Date.now(),
            tool_calls: result.toolCalls,
            status: 'calling' // 标记为调用中
          };
          options.socket.emit(SocketEventType.CHAT_TOOL_CALLING, toolCallingMessage);
        }

        // 执行所有工具调用
        const toolCallResults = await Promise.all(
          result.toolCalls.map(async (toolCall: any) => {
            return await toolManager.executeTool(toolCall.name, toolCall.input);
          })
        );

        // 将工具调用结果添加到结果对象
        result.toolCallResults = toolCallResults;

        // 如果提供了socket实例，发送工具调用完成的状态
        if (options.socket) {
          const toolCalledMessage: any = {
            content: result.content || "我已经获取了工具的结果，正在思考回答...",
            sender: 'ai',
            timestamp: Date.now(),
            tool_calls: result.toolCalls,
            tool_results: toolCallResults,
            status: 'called' // 标记为调用完成
          };
          options.socket.emit(SocketEventType.CHAT_TOOL_CALLED, toolCalledMessage);
        }

        // 使用工具调用结果生成最终回答
        if (modelProvider.continueWithToolResultsUsingMessages) {
          console.log('使用工具调用结果生成最终回答...');

          // 添加助手消息，包含工具调用的思考内容
          if (result.content) {
            messagesCopy.push({
              role: 'assistant',
              content: result.content
            });
          }

          const finalResponse = await modelProvider.continueWithToolResultsUsingMessages(
            messagesCopy,
            result.toolCalls,
            toolCallResults,
            options
          );

          // 更新结果对象的内容
          result.content = finalResponse;
        }
      }

      return result;
    } catch (error) {
      console.error('使用工具生成回答错误:', error);
      throw error;
    }
  }

  /**
   * 使用工具生成回答（兼容旧接口）
   * @param prompt 提示词
   * @param options 生成选项
   * @returns 生成结果
   */
  async generateWithTools(prompt: string, options: any = {}): Promise<any> {
    // 获取当前模型提供商类型
    const providerType = this.getModelProvider();

    // 将提示词转换为消息数组格式
    const messages: Message[] = [
      {
        role: 'user',
        content: prompt
      }
    ];

    // 所有模型都使用专门为工具调用设计的系统提示词
    const systemPrompt = promptService.getToolSystemPrompt();

    if (systemPrompt) {
      messages.unshift({
        role: 'system',
        content: systemPrompt
      });
    }

    return this.generateWithToolsUsingMessages(messages, options);
  }

  /**
   * 获取模型配置
   * @returns 模型配置
   */
  getModelConfig(): any {
    return ModelConfig.getModelConfig();
  }

  /**
   * 获取Ollama模型列表
   * @returns Ollama模型列表
   */
  async getOllamaModels(): Promise<any[]> {
    try {
      return await this.ollamaProvider.getModels();
    } catch (error) {
      console.error('获取Ollama模型列表错误:', error);
      throw error;
    }
  }

  /**
   * 初始化模型服务
   */
  async initialize(): Promise<void> {
    console.log('初始化模型服务...');
    // 加载模型配置
    await ModelConfig.loadConfig();
    console.log('模型服务初始化完成');
    return Promise.resolve();
  }

  /**
   * 获取可用模型列表
   * @param specificProvider 指定的模型提供商，如果不指定则使用当前设置的提供商
   * @returns 模型列表
   */
  async getAvailableModels(specificProvider?: string): Promise<any[]> {
    // 如果指定了提供商，则使用指定的提供商，否则使用当前设置的提供商
    const provider = specificProvider || this.getModelProvider();

    try {
      if (provider === 'ollama') {
        return await this.getOllamaModels();
      } else if (provider === 'qwen') {
        // 通义模型是固定的
        return [
          { id: 'qwen-max', name: 'qwen-max' },
          { id: 'qwen-plus', name: 'qwen-plus' },
          { id: 'qwen-turbo', name: 'qwen-turbo' }
        ];
      } else if (provider === 'deepseek') {
        // DeepSeek模型是固定的
        return [
          { id: 'deepseek-chat', name: 'deepseek-chat' },
          { id: 'deepseek-coder', name: 'deepseek-coder' }
        ];
      }

      return [];
    } catch (error) {
      console.error('获取可用模型列表错误:', error);
      return [];
    }
  }

  /**
   * 更新模型配置
   * @param config 新的配置
   */
  updateModelConfig(config: any): boolean {
    try {
      ModelConfig.updateConfig(config);
      return true;
    } catch (error) {
      console.error('更新模型配置错误:', error);
      return false;
    }
  }

  /**
   * 使用消息数组生成文本流
   * @param messages 消息数组
   * @param options 生成选项
   */
  async *generateTextStreamWithMessages(messages: Message[], options: any = {}): AsyncGenerator<string> {
    const modelProvider = this.getModelProviderInstance();
    const providerType = this.getModelProvider();
    let model = options.model || this.getDefaultModel();

    // 确保使用正确的模型名称格式
    // 如果当前提供商是通义，但模型名称是Ollama格式（包含冒号），则使用默认的通义模型名称
    if (providerType === 'qwen' && model.includes(':')) {
      console.log(`文本流: 当前提供商是通义，但检测到Ollama格式的模型名称: ${model}，将使用默认通义模型: qwen-max`);
      model = 'qwen-max';
    }
    // 如果当前提供商是DeepSeek，但模型名称是Ollama格式（包含冒号），则使用默认的DeepSeek模型名称
    else if (providerType === 'deepseek' && model.includes(':')) {
      console.log(`文本流: 当前提供商是DeepSeek，但检测到Ollama格式的模型名称: ${model}，将使用默认DeepSeek模型: deepseek-chat`);
      model = 'deepseek-chat';
    }
    // 如果当前提供商是Ollama，但模型名称是通义或DeepSeek格式（不包含冒号），则使用默认的Ollama模型名称
    else if (providerType === 'ollama' && !model.includes(':')) {
      const defaultOllamaModel = settingService.getSetting('ollama_model') || 'qwen3:1.7b';
      console.log(`文本流: 当前提供商是Ollama，但检测到非Ollama格式的模型名称: ${model}，将使用默认Ollama模型: ${defaultOllamaModel}`);
      model = defaultOllamaModel;
    }

    try {
      // 创建消息数组的副本，避免修改原始数组
      let messagesCopy = [...messages];

      // 如果指定了对话ID，则添加对话历史
      if (options.conversationId) {
        const { messages: conversationMessages } = await conversationService.getConversationById(options.conversationId);

        // 获取历史窗口大小
        const historyWindowSize = settingService.getHistoryWindowSize();

        // 获取最近的消息
        const recentMessages = conversationMessages.slice(-historyWindowSize);

        // 将对话历史转换为消息数组格式
        const historyMessagesArray: Message[] = recentMessages.map(msg => ({
          role: msg.sender === 'user' ? 'user' : 'assistant',
          content: msg.content
        }));

        // 将历史消息添加到消息数组前面
        messagesCopy = [...historyMessagesArray, ...messagesCopy];
      }

      // 如果启用了记忆，则添加相关记忆
      if (options.enableMemory !== false) {
        // 获取用户最后一条消息的内容用于查找相关记忆
        const userMessages = messagesCopy.filter(msg => msg.role === 'user');
        const lastUserMessage = userMessages.length > 0 ? userMessages[userMessages.length - 1].content : '';
        const memories = await memoryService.findRelatedMemories(lastUserMessage);

        if (memories.length > 0) {
          // 构建记忆提示
          const memoryContent = promptService.getMemoriesPrompt(memories);

          // 添加系统消息到消息数组前面
          const systemMessage: Message = {
            role: 'system',
            content: memoryContent
          };

          // 检查是否已有系统消息
          const hasSystemMessage = messagesCopy.some(msg => msg.role === 'system');

          if (hasSystemMessage) {
            // 如果已有系统消息，则更新第一条系统消息
            const systemIndex = messagesCopy.findIndex(msg => msg.role === 'system');
            messagesCopy[systemIndex].content += '\n\n' + memoryContent;
          } else {
            // 如果没有系统消息，则添加到消息数组前面
            messagesCopy = [systemMessage, ...messagesCopy];
          }
        }
      }

      // 检查模型提供商是否支持文本流
      if (modelProvider.generateTextStreamWithMessages) {
        // 调用模型提供商生成文本流
        const stream = modelProvider.generateTextStreamWithMessages(messagesCopy, {
          ...options,
          model
        });

        for await (const chunk of stream) {
          yield chunk;
        }
      } else {
        // 如果不支持流式生成，则使用普通生成并一次性返回
        const text = await modelProvider.generateTextWithMessages(messagesCopy, {
          ...options,
          model
        });
        yield text;
      }
    } catch (error) {
      console.error('生成文本流错误:', error);
      throw error;
    }
  }

  /**
   * 生成文本流（兼容旧接口）
   * @param prompt 提示词
   * @param options 生成选项
   */
  async *generateTextStream(prompt: string, options: any = {}): AsyncGenerator<string> {
    // 获取当前模型提供商类型
    const providerType = this.getModelProvider();

    // 将提示词转换为消息数组格式
    const messages: Message[] = [
      {
        role: 'user',
        content: prompt
      }
    ];

    // 所有模型都使用通用系统提示词
    const systemPrompt = promptService.getUniversalSystemPrompt();

    if (systemPrompt) {
      messages.unshift({
        role: 'system',
        content: systemPrompt
      });
    }

    // 使用消息数组生成文本流
    const stream = this.generateTextStreamWithMessages(messages, options);

    for await (const chunk of stream) {
      yield chunk;
    }
  }

  /**
   * 在思考步骤中执行工具调用并继续思考
   * @param question 原始问题
   * @param stepTitle 步骤标题
   * @param previousSteps 之前步骤的结果
   * @param stepResult 当前步骤的初始结果
   * @param stepHistory 当前步骤的完整对话历史
   * @param options 生成选项
   * @returns 步骤执行结果
   */
  private async executeToolsInThinkingStep(
    question: string,
    stepTitle: string,
    previousSteps: any[] = [],
    stepResult: any,
    stepHistory: string | null = null,
    options: any = {}
  ): Promise<any> {
    // 获取步骤ID，用于实时更新操作日志
    const stepId = options.stepId;
    // 获取默认模型
    const model = options.model || this.getDefaultModel();

    // 添加重试计数器
    const retryCount = options.retryCount || 0;
    const maxRetries = 3; // 最大重试次数

    // 添加操作日志记录
    if (!stepResult.operations_log) {
      stepResult.operations_log = [];
    }

    // 记录开始执行工具调用
    const toolCallsCount = stepResult.tool_calls ? stepResult.tool_calls.length : 0;
    const operationLog = `开始执行 ${toolCallsCount} 个工具调用`;
    stepResult.operations_log.push(`${operationLog} (${new Date().toLocaleTimeString()})`);

    // 实时更新操作日志
    if (stepId && (global as any).updateThinkingStepOperationLog) {
      (global as any).updateThinkingStepOperationLog(stepId, operationLog);
    }

    try {
      // 执行所有工具调用
      const toolResults = await Promise.all(
        stepResult.tool_calls.map(async (toolCall: any, index: number) => {
          // 记录开始执行特定工具
          const startToolLog = `开始执行工具 ${toolCall.name}`;
          stepResult.operations_log.push(`${startToolLog} (${new Date().toLocaleTimeString()})`);

          // 实时更新操作日志
          if (stepId && (global as any).updateThinkingStepOperationLog) {
            (global as any).updateThinkingStepOperationLog(stepId, startToolLog);
          }

          try {
            const result = await toolManager.executeTool(toolCall.name, toolCall.input);
            // 记录工具执行成功
            const successLog = `工具 ${toolCall.name} 执行成功`;
            stepResult.operations_log.push(`${successLog} (${new Date().toLocaleTimeString()})`);

            // 实时更新操作日志
            if (stepId && (global as any).updateThinkingStepOperationLog) {
              (global as any).updateThinkingStepOperationLog(stepId, successLog);
            }

            return result;
          } catch (toolError) {
            // 如果单个工具调用失败，返回错误信息
            console.error(`工具 ${toolCall.name} 调用失败:`, toolError);
            const errorMessage = toolError.message || String(toolError);
            // 记录工具执行失败
            const failureLog = `工具 ${toolCall.name} 执行失败: ${errorMessage}`;
            stepResult.operations_log.push(`${failureLog} (${new Date().toLocaleTimeString()})`);

            // 实时更新操作日志
            if (stepId && (global as any).updateThinkingStepOperationLog) {
              (global as any).updateThinkingStepOperationLog(stepId, failureLog);
            }

            return {
              tool_output: `执行失败: ${errorMessage}`,
              error: true
            };
          }
        })
      );

      // 检查是否有工具调用失败
      const hasFailedTools = toolResults.some(result => result.error === true);

      // 将工具调用结果添加到步骤结果中
      stepResult.tool_results = toolResults;

      // 如果有工具调用失败，但还没有达到最大重试次数，尝试让模型处理失败情况
      if (hasFailedTools && retryCount < maxRetries) {
        console.log(`检测到工具调用失败，正在尝试让模型处理失败情况...`);

        // 记录开始处理工具调用失败
        const failureHandlingLog = `检测到工具调用失败，正在尝试让大模型处理失败情况`;
        stepResult.operations_log.push(`${failureHandlingLog} (${new Date().toLocaleTimeString()})`);

        // 实时更新操作日志
        if (stepId && (global as any).updateThinkingStepOperationLog) {
          (global as any).updateThinkingStepOperationLog(stepId, failureHandlingLog);
        }

        // 构建工具结果文本，包括失败信息
        const toolResultsText = toolResults.map((result, index) => {
          const toolCall = stepResult.tool_calls[index];
          const resultText = result.error
            ? `工具: ${toolCall.name}\n输入: ${JSON.stringify(toolCall.input)}\n状态: 失败\n错误: ${result.tool_output}`
            : `工具: ${toolCall.name}\n输入: ${JSON.stringify(toolCall.input)}\n输出: ${JSON.stringify(result.tool_output)}`;
          return resultText;
        }).join('\n\n');

        // 获取工具调用失败处理提示词
        const failureHandlingPrompt = promptService.getToolFailureHandlingPrompt(
          question,
          stepTitle,
          stepResult.content,
          toolResultsText,
          stepHistory
        );

        // 记录正在生成失败处理策略
        stepResult.operations_log.push(`正在生成工具调用失败处理策略 (${new Date().toLocaleTimeString()})`);

        // 生成失败处理思考
        const failureHandlingResponse = await this.generateText(failureHandlingPrompt, {
          ...options,
          model,
          temperature: 0.5,
          max_tokens: 4096,
          conversationId: null, // 禁用对话历史
          enableMemory: false,  // 禁用记忆
          skipSystemPrompt: true // 禁用系统提示词
        });

        // 解析失败处理思考的结果
        const { removeThinkTags } = require('../../utils/text-processor');
        const cleanedFailureHandlingResponse = removeThinkTags(failureHandlingResponse);
        const failureHandlingJsonMatch = cleanedFailureHandlingResponse.match(/\{[\s\S]*\}/);

        if (failureHandlingJsonMatch) {
          try {
            const failureHandlingResult = JSON.parse(failureHandlingJsonMatch[0]);

            // 记录失败处理策略生成成功
            const strategySuccessLog = `工具调用失败处理策略生成成功`;
            stepResult.operations_log.push(`${strategySuccessLog} (${new Date().toLocaleTimeString()})`);

            // 实时更新操作日志
            if (stepId && (global as any).updateThinkingStepOperationLog) {
              (global as any).updateThinkingStepOperationLog(stepId, strategySuccessLog);
            }

            // 更新步骤内容，添加失败处理信息
            const updatedContent = stepResult.content + '\n\n工具调用部分失败，分析与处理:\n' + failureHandlingResult.content;

            // 检查是否需要向用户提问
            if (failureHandlingResult.ask_user && failureHandlingResult.question) {
              console.log('工具调用失败后需要向用户提问:', failureHandlingResult.question);

              // 记录需要向用户提问
              const askUserLog = `工具调用失败后，大模型决定向用户提问: ${failureHandlingResult.question}`;
              stepResult.operations_log.push(`${askUserLog} (${new Date().toLocaleTimeString()})`);

              // 实时更新操作日志
              if (stepId && (global as any).updateThinkingStepOperationLog) {
                (global as any).updateThinkingStepOperationLog(stepId, askUserLog);
              }

              return {
                title: stepResult.title,
                content: updatedContent,
                tool_calls: stepResult.tool_calls,
                tool_results: stepResult.tool_results,
                needs_user_input: true,
                question: failureHandlingResult.question,
                operations_log: stepResult.operations_log
              };
            }

            // 检查是否需要使用不同的工具
            if (failureHandlingResult.use_tools && failureHandlingResult.tool_calls && failureHandlingResult.tool_calls.length > 0) {
              console.log('工具调用失败后尝试使用不同的工具...');

              // 记录尝试使用不同的工具
              const useNewToolsLog = `工具调用失败后，大模型决定尝试使用不同的工具`;
              stepResult.operations_log.push(`${useNewToolsLog} (${new Date().toLocaleTimeString()})`);

              // 实时更新操作日志
              if (stepId && (global as any).updateThinkingStepOperationLog) {
                (global as any).updateThinkingStepOperationLog(stepId, useNewToolsLog);
              }

              failureHandlingResult.tool_calls.forEach((toolCall: any) => {
                const newToolLog = `- 准备调用替代工具: ${toolCall.name}`;
                stepResult.operations_log.push(`${newToolLog} (${new Date().toLocaleTimeString()})`);

                // 实时更新操作日志
                if (stepId && (global as any).updateThinkingStepOperationLog) {
                  (global as any).updateThinkingStepOperationLog(stepId, newToolLog);
                }
              });

              // 创建新的步骤结果
              const newStepResult = {
                ...failureHandlingResult,
                content: updatedContent,
                operations_log: stepResult.operations_log // 保留操作日志
              };

              // 递归调用自身，继续执行工具调用，增加重试计数
              return await this.executeToolsInThinkingStep(
                question,
                stepTitle,
                previousSteps,
                newStepResult,
                stepHistory ? stepHistory + '\n\n' + toolResultsText : toolResultsText,
                {
                  ...options,
                  retryCount: retryCount + 1
                }
              );
            }

            // 如果模型决定直接完成步骤
            if (failureHandlingResult.conclusion) {
              console.log('工具调用部分失败，但模型决定直接完成步骤');

              // 记录模型决定直接完成步骤
              const completeStepLog = `工具调用部分失败，但大模型决定直接完成步骤`;
              stepResult.operations_log.push(`${completeStepLog} (${new Date().toLocaleTimeString()})`);

              // 实时更新操作日志
              if (stepId && (global as any).updateThinkingStepOperationLog) {
                (global as any).updateThinkingStepOperationLog(stepId, completeStepLog);
              }

              const conclusionLog = `结论: ${failureHandlingResult.conclusion.substring(0, 100)}...`;
              stepResult.operations_log.push(`${conclusionLog} (${new Date().toLocaleTimeString()})`);

              // 实时更新操作日志
              if (stepId && (global as any).updateThinkingStepOperationLog) {
                (global as any).updateThinkingStepOperationLog(stepId, conclusionLog);
              }

              return {
                title: stepResult.title,
                content: updatedContent,
                tool_calls: stepResult.tool_calls,
                tool_results: stepResult.tool_results,
                conclusion: failureHandlingResult.conclusion,
                operations_log: stepResult.operations_log
              };
            }
          } catch (parseError) {
            console.error('解析失败处理JSON错误:', parseError);
          }
        }
      }

      // 构建工具结果文本
      let toolResultsText = toolResults.map((result, index) => {
        const toolCall = stepResult.tool_calls[index];
        return `工具: ${toolCall.name}\n输入: ${JSON.stringify(toolCall.input)}\n输出: ${JSON.stringify(result.tool_output)}`;
      }).join('\n\n');

      // 记录所有工具调用完成，准备继续思考
      const continueThinkingLog = `所有工具调用完成，准备继续思考`;
      stepResult.operations_log.push(`${continueThinkingLog} (${new Date().toLocaleTimeString()})`);

      // 实时更新操作日志
      if (stepId && (global as any).updateThinkingStepOperationLog) {
        (global as any).updateThinkingStepOperationLog(stepId, continueThinkingLog);
      }

      // 构建步骤历史文本
      let fullStepHistoryText = '';
      if (stepHistory) {
        fullStepHistoryText = stepHistory;
      }

      // 添加当前步骤的思考和工具调用结果
      fullStepHistoryText += `\n\n当前思考: ${stepResult.content}\n\n工具调用结果:\n${toolResultsText}\n\n`;

      // 获取工具调用后继续思考提示词
      const continuePrompt = promptService.getToolContinueThinkingPrompt(
        question,
        stepTitle,
        fullStepHistoryText
      );

      // 记录正在生成工具调用后的思考
      const generatingThinkingLog = `正在生成工具调用后的思考`;
      stepResult.operations_log.push(`${generatingThinkingLog} (${new Date().toLocaleTimeString()})`);

      // 实时更新操作日志
      if (stepId && (global as any).updateThinkingStepOperationLog) {
        (global as any).updateThinkingStepOperationLog(stepId, generatingThinkingLog);
      }

      // 生成继续思考的回答
      const continueResponse = await this.generateText(continuePrompt, {
        ...options,
        model,
        temperature: 0.3,
        max_tokens: 4096,
        conversationId: null, // 禁用对话历史
        enableMemory: false,  // 禁用记忆
        skipSystemPrompt: true // 禁用系统提示词
      });

      // 解析继续思考的结果
      const { removeThinkTags } = require('../../utils/text-processor');
      const cleanedContinueResponse = removeThinkTags(continueResponse);
      const continueJsonMatch = cleanedContinueResponse.match(/\{[\s\S]*\}/);

      if (continueJsonMatch) {
        const continueResult = JSON.parse(continueJsonMatch[0]);

        // 记录思考生成成功
        const thinkingSuccessLog = `工具调用后的思考生成成功`;
        stepResult.operations_log.push(`${thinkingSuccessLog} (${new Date().toLocaleTimeString()})`);

        // 实时更新操作日志
        if (stepId && (global as any).updateThinkingStepOperationLog) {
          (global as any).updateThinkingStepOperationLog(stepId, thinkingSuccessLog);
        }

        // 更新步骤内容，添加工具调用结果
        const updatedContent = stepResult.content + '\n\n工具调用结果分析:\n' + continueResult.content;

        // 检查是否需要向用户提问
        if (continueResult.ask_user && continueResult.question) {
          console.log('工具调用后步骤需要向用户提问:', continueResult.question);

          // 记录需要向用户提问
          const askUserLog = `工具调用后，大模型决定向用户提问: ${continueResult.question}`;
          stepResult.operations_log.push(`${askUserLog} (${new Date().toLocaleTimeString()})`);

          // 实时更新操作日志
          if (stepId && (global as any).updateThinkingStepOperationLog) {
            (global as any).updateThinkingStepOperationLog(stepId, askUserLog);
          }

          return {
            title: stepResult.title,
            content: updatedContent,
            tool_calls: stepResult.tool_calls,
            tool_results: stepResult.tool_results,
            needs_user_input: true,
            question: continueResult.question,
            operations_log: stepResult.operations_log
          };
        }

        // 检查是否需要继续使用工具
        if (continueResult.use_tools && continueResult.tool_calls && continueResult.tool_calls.length > 0) {
          console.log('工具调用后步骤需要继续使用工具');

          // 记录需要继续使用工具
          const continueUseToolsLog = `工具调用后，大模型决定继续使用工具`;
          stepResult.operations_log.push(`${continueUseToolsLog} (${new Date().toLocaleTimeString()})`);

          // 实时更新操作日志
          if (stepId && (global as any).updateThinkingStepOperationLog) {
            (global as any).updateThinkingStepOperationLog(stepId, continueUseToolsLog);
          }

          continueResult.tool_calls.forEach((toolCall: any) => {
            const prepareToolLog = `- 准备调用工具: ${toolCall.name}`;
            stepResult.operations_log.push(`${prepareToolLog} (${new Date().toLocaleTimeString()})`);

            // 实时更新操作日志
            if (stepId && (global as any).updateThinkingStepOperationLog) {
              (global as any).updateThinkingStepOperationLog(stepId, prepareToolLog);
            }
          });

          // 递归调用自身，继续执行工具调用
          const nestedStepResult = {
            ...continueResult,
            content: updatedContent,
            operations_log: stepResult.operations_log // 保留操作日志
          };

          return await this.executeToolsInThinkingStep(
            question,
            stepTitle,
            previousSteps,
            nestedStepResult,
            fullStepHistoryText,
            options
          );
        }

        // 如果不需要继续提问或使用工具，返回最终结果
        // 记录步骤完成
        if (continueResult.conclusion) {
          const conclusionLog = `步骤完成，生成结论: ${continueResult.conclusion.substring(0, 100)}...`;
          stepResult.operations_log.push(`${conclusionLog} (${new Date().toLocaleTimeString()})`);

          // 实时更新操作日志
          if (stepId && (global as any).updateThinkingStepOperationLog) {
            (global as any).updateThinkingStepOperationLog(stepId, conclusionLog);
          }
        } else {
          const noConclustionLog = `步骤完成，但未提供明确结论`;
          stepResult.operations_log.push(`${noConclustionLog} (${new Date().toLocaleTimeString()})`);

          // 实时更新操作日志
          if (stepId && (global as any).updateThinkingStepOperationLog) {
            (global as any).updateThinkingStepOperationLog(stepId, noConclustionLog);
          }
        }

        return {
          title: stepResult.title,
          content: updatedContent,
          tool_calls: stepResult.tool_calls,
          tool_results: stepResult.tool_results,
          images: continueResult.images || [],
          conclusion: continueResult.conclusion || '此步骤已完成，但未提供明确结论。',
          operations_log: stepResult.operations_log
        };
      }

      // 如果无法解析JSON，返回一个基本结果
      // 记录无法解析JSON
      const parseErrorLog = `无法解析工具调用后的思考结果`;
      stepResult.operations_log.push(`${parseErrorLog} (${new Date().toLocaleTimeString()})`);

      // 实时更新操作日志
      if (stepId && (global as any).updateThinkingStepOperationLog) {
        (global as any).updateThinkingStepOperationLog(stepId, parseErrorLog);
      }

      return {
        title: stepResult.title,
        content: stepResult.content + '\n\n工具调用已完成，但无法生成有效的后续分析。',
        tool_calls: stepResult.tool_calls,
        tool_results: stepResult.tool_results,
        conclusion: '此步骤已完成工具调用，但未能生成有效结论。',
        operations_log: stepResult.operations_log
      };
    } catch (error) {
      console.error('在思考步骤中执行工具调用错误:', error);

      // 记录工具调用错误
      const errorMessage = error instanceof Error ? error.message : String(error);
      const errorLog = `执行工具调用时出现错误: ${errorMessage}`;
      stepResult.operations_log.push(`${errorLog} (${new Date().toLocaleTimeString()})`);

      // 实时更新操作日志
      if (stepId && (global as any).updateThinkingStepOperationLog) {
        (global as any).updateThinkingStepOperationLog(stepId, errorLog);
      }

      // 如果还没有达到最大重试次数，尝试重试
      if (retryCount < maxRetries) {
        console.log(`工具调用失败，正在进行第 ${retryCount + 1} 次重试...`);

        // 记录准备重试
        const retryLog = `工具调用失败，准备进行第 ${retryCount + 1} 次重试`;
        stepResult.operations_log.push(`${retryLog} (${new Date().toLocaleTimeString()})`);

        // 实时更新操作日志
        if (stepId && (global as any).updateThinkingStepOperationLog) {
          (global as any).updateThinkingStepOperationLog(stepId, retryLog);
        }

        // 获取工具调用重试提示词
        const retryPrompt = promptService.getToolRetryPrompt(
          question,
          stepTitle,
          stepResult.content,
          errorMessage,
          stepHistory
        );

        // 生成重试思考
        const retryResponse = await this.generateText(retryPrompt, {
          ...options,
          model,
          temperature: 0.5, // 稍微提高温度，增加多样性
          max_tokens: 4096,
          conversationId: null, // 禁用对话历史
          enableMemory: false,  // 禁用记忆
          skipSystemPrompt: true // 禁用系统提示词
        });

        // 解析重试思考的结果
        const { removeThinkTags } = require('../../utils/text-processor');
        const cleanedRetryResponse = removeThinkTags(retryResponse);
        const retryJsonMatch = cleanedRetryResponse.match(/\{[\s\S]*\}/);

        if (retryJsonMatch) {
          try {
            const retryResult = JSON.parse(retryJsonMatch[0]);

            // 记录重试策略生成成功
            const retryStrategyLog = `重试策略生成成功`;
            stepResult.operations_log.push(`${retryStrategyLog} (${new Date().toLocaleTimeString()})`);

            // 实时更新操作日志
            if (stepId && (global as any).updateThinkingStepOperationLog) {
              (global as any).updateThinkingStepOperationLog(stepId, retryStrategyLog);
            }

            // 如果重试结果包含工具调用，递归执行
            if (retryResult.use_tools && retryResult.tool_calls && retryResult.tool_calls.length > 0) {
              console.log('使用新的工具调用重试...');

              // 记录使用新的工具调用重试
              const newToolsRetryLog = `大模型决定使用新的工具调用重试`;
              stepResult.operations_log.push(`${newToolsRetryLog} (${new Date().toLocaleTimeString()})`);

              // 实时更新操作日志
              if (stepId && (global as any).updateThinkingStepOperationLog) {
                (global as any).updateThinkingStepOperationLog(stepId, newToolsRetryLog);
              }

              retryResult.tool_calls.forEach((toolCall: any) => {
                const newToolLog = `- 准备调用替代工具: ${toolCall.name}`;
                stepResult.operations_log.push(`${newToolLog} (${new Date().toLocaleTimeString()})`);

                // 实时更新操作日志
                if (stepId && (global as any).updateThinkingStepOperationLog) {
                  (global as any).updateThinkingStepOperationLog(stepId, newToolLog);
                }
              });

              // 更新步骤内容，添加重试信息
              const updatedContent = stepResult.content +
                '\n\n工具调用失败: ' + (error instanceof Error ? error.message : String(error)) +
                '\n\n重试思考: ' + retryResult.content;

              // 创建新的步骤结果
              const newStepResult = {
                ...retryResult,
                content: updatedContent,
                operations_log: stepResult.operations_log // 保留操作日志
              };

              // 递归调用自身，继续执行工具调用，增加重试计数
              return await this.executeToolsInThinkingStep(
                question,
                stepTitle,
                previousSteps,
                newStepResult,
                stepHistory,
                {
                  ...options,
                  retryCount: retryCount + 1
                }
              );
            } else if (retryResult.conclusion) {
              // 如果模型决定放弃使用工具，直接返回结论
              const abandonToolsLog = `大模型决定放弃使用工具，直接生成结论`;
              stepResult.operations_log.push(`${abandonToolsLog} (${new Date().toLocaleTimeString()})`);

              // 实时更新操作日志
              if (stepId && (global as any).updateThinkingStepOperationLog) {
                (global as any).updateThinkingStepOperationLog(stepId, abandonToolsLog);
              }

              const conclusionLog = `结论: ${retryResult.conclusion.substring(0, 100)}...`;
              stepResult.operations_log.push(`${conclusionLog} (${new Date().toLocaleTimeString()})`);

              // 实时更新操作日志
              if (stepId && (global as any).updateThinkingStepOperationLog) {
                (global as any).updateThinkingStepOperationLog(stepId, conclusionLog);
              }

              // 更新步骤内容，添加放弃信息
              const updatedContent = stepResult.content +
                '\n\n工具调用失败: ' + (error instanceof Error ? error.message : String(error)) +
                '\n\n放弃工具调用思考: ' + retryResult.content;

              return {
                title: stepResult.title,
                content: updatedContent,
                tool_calls: stepResult.tool_calls,
                tool_results: stepResult.tool_results,
                conclusion: retryResult.conclusion,
                operations_log: stepResult.operations_log
              };
            }
          } catch (parseError) {
            console.error('解析重试JSON错误:', parseError);
            stepResult.operations_log.push(`解析重试JSON错误: ${parseError.message} (${new Date().toLocaleTimeString()})`);
          }
        } else {
          stepResult.operations_log.push(`无法解析重试策略 (${new Date().toLocaleTimeString()})`);
        }
      }

      // 如果重试次数已达上限或重试失败，返回错误结果但提供建设性建议
      const errorContent = stepResult.content + '\n\n执行工具调用时出现错误: ' + (error instanceof Error ? error.message : String(error)) +
        (retryCount > 0 ? `\n\n已尝试重试 ${retryCount} 次，但仍然失败。` : '') +
        '\n\n可能的解决方案:\n' +
        '1. 检查文件路径是否正确\n' +
        '2. 尝试使用不同的工具（如read_file替代safe_shell）\n' +
        '3. 检查文件内容是否包含特殊字符\n' +
        '4. 考虑手动提供必要信息以继续任务';

      const conclusion = '此步骤在执行工具调用时遇到技术困难。系统已尝试多种方法但未能解决。建议尝试替代方案或提供额外信息以继续完成任务。';

      // 记录达到最大重试次数或重试失败
      const maxRetriesLog = `达到最大重试次数或重试失败，返回错误结果`;
      stepResult.operations_log.push(`${maxRetriesLog} (${new Date().toLocaleTimeString()})`);

      // 实时更新操作日志
      if (stepId && (global as any).updateThinkingStepOperationLog) {
        (global as any).updateThinkingStepOperationLog(stepId, maxRetriesLog);
      }

      const finalConclusionLog = `最终结论: ${conclusion.substring(0, 100)}...`;
      stepResult.operations_log.push(`${finalConclusionLog} (${new Date().toLocaleTimeString()})`);

      // 实时更新操作日志
      if (stepId && (global as any).updateThinkingStepOperationLog) {
        (global as any).updateThinkingStepOperationLog(stepId, finalConclusionLog);
      }

      return {
        title: stepResult.title,
        content: errorContent,
        tool_calls: stepResult.tool_calls,
        error: error instanceof Error ? error.message : String(error),
        conclusion: conclusion,
        operations_log: stepResult.operations_log
      };
    }
  }
}
