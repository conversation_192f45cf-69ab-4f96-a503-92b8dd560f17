import fetch from 'node-fetch';
import { ModelProvider, Message } from '../interfaces/model-provider.interface';
import { settingService } from '../../settings';

/**
 * 通义千问模型提供商
 * 实现与通义千问API的交互
 */
export class QwenProvider implements ModelProvider {
  /**
   * 获取通义大模型 API URL
   * @returns 通义大模型 API URL
   */
  private getQwenApiUrl(): string {
    // 优先使用数据库中的设置，如果不存在则使用环境变量，最后使用默认值
    const dbSetting = settingService.getSetting('qwen_api_url');
    if (dbSetting) {
      return dbSetting;
    }
    return process.env.QWEN_API_URL || 'https://dashscope.aliyuncs.com/compatible-mode/v1/chat/completions';
  }

  /**
   * 获取通义大模型 API Key
   * @returns 通义大模型 API Key
   */
  private getQwenApiKey(): string {
    // 优先使用数据库中的设置，如果不存在则使用环境变量
    const dbSetting = settingService.getSetting('qwen_api_key');
    if (dbSetting) {
      return dbSetting;
    }
    return process.env.QWEN_API_KEY || '';
  }

  /**
   * 获取通义大模型是否启用联网搜索
   * @returns 是否启用联网搜索
   */
  private isSearchEnabled(): boolean {
    return settingService.isQwenSearchEnabled();
  }

  /**
   * 使用消息数组生成文本
   * @param messages 消息数组
   * @param options 生成选项
   * @returns 生成的文本
   */
  async generateTextWithMessages(messages: Message[], options: any = {}): Promise<string> {
    try {
      // 确保使用通义大模型API时使用正确的模型名称
      // 如果传入的是Ollama模型名称（如qwen3:1.7b），则使用默认的通义模型名称
      let model = options.model || 'qwen-max';

      // 检查模型名称是否为Ollama格式（包含冒号），如果是则使用默认的通义模型
      if (model.includes(':')) {
        console.log(`检测到Ollama格式的模型名称: ${model}，将使用默认通义模型: qwen-max`);
        model = 'qwen-max';
      }

      console.log(`通义大模型API请求: ${this.getQwenApiUrl()}, 模型: ${model}`);

      // 打印完整的输入内容
      console.log('通义千问生成文本完整输入(消息数组):');
      console.log('---开始输入---');
      console.log(JSON.stringify(messages, null, 2));
      console.log('---结束输入---');

      const response = await fetch(this.getQwenApiUrl(), {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${this.getQwenApiKey()}`,
        },
        body: JSON.stringify({
          model,
          messages,
          temperature: options.temperature || 0.7,
          top_p: options.top_p || 0.9,
          max_tokens: options.max_tokens || 2048,
          stream: false,
          enable_search: options.enableSearch !== undefined ? options.enableSearch : this.isSearchEnabled(),
        }),
      });

      if (!response.ok) {
        const errorText = await response.text();
        console.error(`通义大模型API错误: ${response.status} ${response.statusText}, 详情: ${errorText}`);
        throw new Error(`通义大模型API错误: ${response.status} ${response.statusText}`);
      }

      const data = await response.json();

      // 打印大模型原始输出到控制台
      console.log('通义千问大模型原始输出:');
      console.log('---开始输出---');
      console.log(data.choices[0].message.content);
      console.log('---结束输出---');

      return data.choices[0].message.content;
    } catch (error) {
      console.error('通义大模型生成文本错误:', error);
      throw error;
    }
  }

  /**
   * 生成文本（兼容旧接口）
   * @param prompt 提示词
   * @param options 生成选项
   * @returns 生成的文本
   */
  async generateText(prompt: string, options: any = {}): Promise<string> {
    // 将单一提示词转换为消息数组格式
    const messages: Message[] = [
      {
        role: 'user',
        content: prompt,
      },
    ];

    return this.generateTextWithMessages(messages, options);
  }

  /**
   * 深度思考
   * @param question 问题
   * @param options 生成选项
   * @returns 思考结果
   */
  async deepThinking(question: string, options: any = {}): Promise<any> {
    try {
      // 导入提示词服务
      const { promptService } = require('../prompts');

      // 获取深度思考提示词
      const prompt = promptService.getDeepThinkingPrompt(question);

      const response = await this.generateText(prompt, {
        ...options,
        max_tokens: 4096
      });

      // 尝试解析JSON
      try {
        // 导入文本处理工具
        const { removeThinkTags, fixJsonString } = require('../../../utils/text-processor');

        // 移除<think>标签，避免JSON解析错误
        const cleanedResponse = removeThinkTags(response);

        // 提取JSON部分
        const jsonMatch = cleanedResponse.match(/\{[\s\S]*\}/);
        if (jsonMatch) {
          // 修复JSON字符串中的特殊字符
          const fixedJsonStr = fixJsonString(jsonMatch[0]);

          // 解析修复后的JSON
          return JSON.parse(fixedJsonStr);
        }
        return null;
      } catch (error) {
        console.error('解析深度思考JSON错误:', error);
        // 在catch块中不能访问try块中的变量，所以不能直接使用jsonMatch
        return null;
      }
    } catch (error) {
      console.error('深度思考错误:', error);
      return null;
    }
  }

  /**
   * 生成记忆
   * @param context 上下文
   * @param options 生成选项
   * @returns 生成的记忆数据
   */
  async generateMemory(context: string, options: any = {}): Promise<any> {
    try {
      // 导入提示词服务
      const { promptService } = require('../prompts');

      // 获取记忆生成提示词
      const prompt = promptService.getMemoryGenerationPrompt(context);

      const response = await this.generateText(prompt, options);

      // 如果没有值得记忆的内容
      if (response.includes('NO_MEMORY')) {
        return null;
      }

      // 尝试解析JSON
      try {
        // 导入文本处理工具
        const { removeThinkTags, fixJsonString } = require('../../../utils/text-processor');

        // 移除<think>标签，避免JSON解析错误
        const cleanedResponse = removeThinkTags(response);

        // 提取JSON部分
        const jsonMatch = cleanedResponse.match(/\{[\s\S]*\}/);
        if (jsonMatch) {
          // 修复JSON字符串中的特殊字符
          const fixedJsonStr = fixJsonString(jsonMatch[0]);

          // 解析修复后的JSON
          const memoryData = JSON.parse(fixedJsonStr);

          // 确认这是重要记忆
          if (!memoryData.is_important) {
            console.log('记忆被判断为不重要，不保存');
            return null;
          }

          return {
            keywords: memoryData.keywords,
            content: memoryData.content,
            memory_type: memoryData.memory_type || 'factual',
            memory_subtype: memoryData.memory_subtype || null,
            importance: memoryData.importance,
            is_pinned: memoryData.is_pinned || false,
            context: context.substring(0, 500), // 保存部分上下文
          };
        }
        return null;
      } catch (error) {
        console.error('解析记忆JSON错误:', error);
        // 在catch块中不能访问try块中的变量，所以不能直接使用jsonMatch
        return null;
      }
    } catch (error) {
      console.error('生成记忆错误:', error);
      return null;
    }
  }

  /**
   * 使用工具生成回答（使用消息数组）
   * @param messages 消息数组
   * @param tools 可用工具列表
   * @param options 生成选项
   * @returns 工具调用结果
   */
  async generateWithToolsUsingMessages(messages: Message[], tools: any[], options: any = {}): Promise<any> {
    try {
      // 导入提示词服务
      const { promptService } = require('../prompts');

      // 创建系统提示消息，使用专门为工具调用设计的系统提示词
      const systemMessage: Message = {
        role: 'system',
        content: promptService.getToolSystemPrompt()
      };

      // 创建新的消息数组，添加系统提示
      const messagesWithSystem = [systemMessage, ...messages];

      // 打印完整的输入内容
      console.log('通义千问工具调用完整输入(消息数组):');
      console.log('---开始输入---');
      console.log(JSON.stringify(messagesWithSystem, null, 2));
      console.log('---结束输入---');

      // 生成回答
      const response = await this.generateTextWithMessages(messagesWithSystem, {
        ...options,
        temperature: 0.2,
        max_tokens: 4096
      });

      // 尝试解析JSON
      try {
        // 导入文本处理工具
        const { parseToolCallJson } = require('../../../utils/text-processor');

        // 使用统一的工具调用JSON解析函数
        return parseToolCallJson(response);
      } catch (error) {
        console.error('解析工具调用JSON错误:', error);
        return { content: response, toolCalls: [] };
      }
    } catch (error) {
      console.error('使用工具生成回答错误:', error);
      throw error;
    }
  }

  /**
   * 重试工具调用
   * @param prompt 提示词
   * @param tools 可用工具列表
   * @param options 生成选项
   * @param maxRetries 最大重试次数
   * @returns 工具调用结果
   */
  async retryToolCall(prompt: string, tools: any[], options: any = {}, maxRetries: number = 3): Promise<any> {
    let retries = 0;
    let error = null;

    while (retries < maxRetries) {
      try {
        // 将提示词转换为消息数组
        const messages: Message[] = [
          {
            role: 'user',
            content: prompt
          }
        ];

        const result = await this.generateWithToolsUsingMessages(messages, tools, options);
        return result;
      } catch (err) {
        error = err;
        retries++;
        console.warn(`工具调用失败，重试 ${retries}/${maxRetries}:`, err);
        // 等待一段时间再重试
        await new Promise(resolve => setTimeout(resolve, 1000));
      }
    }

    console.error(`工具调用在 ${maxRetries} 次重试后仍然失败:`, error);
    throw error;
  }

  /**
   * 继续处理工具调用结果（使用消息数组）
   * @param messages 消息数组
   * @param toolCalls 工具调用列表
   * @param toolCallResults 工具调用结果
   * @param options 生成选项
   * @returns 生成的文本
   */
  async continueWithToolResultsUsingMessages(
    messages: Message[],
    toolCalls: any[],
    toolCallResults: any[],
    options: any = {}
  ): Promise<string> {
    try {
      // 导入提示词服务
      const { promptService } = require('../prompts');

      // 创建系统提示消息，使用专门为工具结果处理设计的系统提示词
      const systemMessage: Message = {
        role: 'system',
        content: promptService.getToolResultsSystemPrompt()
      };

      // 创建新的消息数组，添加系统提示
      const messagesWithSystem = [systemMessage, ...messages];

      // 打印完整的输入内容
      console.log('通义千问工具结果继续对话完整输入(消息数组):');
      console.log('---开始输入---');
      console.log(JSON.stringify(messagesWithSystem, null, 2));
      console.log('---结束输入---');

      // 生成最终回答
      return await this.generateTextWithMessages(messagesWithSystem, options);
    } catch (error) {
      console.error('继续对话错误:', error);
      throw error;
    }
  }

  /**
   * 继续处理工具调用结果（兼容旧接口）
   * @param prompt 原始提示词
   * @param previousResponse 之前的响应
   * @param toolCalls 工具调用列表
   * @param toolCallResults 工具调用结果
   * @param options 生成选项
   * @returns 生成的文本
   */
  async continueWithToolResults(
    prompt: string,
    previousResponse: string,
    toolCalls: any[],
    toolCallResults: any[],
    options: any = {}
  ): Promise<string> {
    try {
      // 将提示词和回答转换为消息数组
      const messages: Message[] = [
        {
          role: 'user',
          content: prompt
        },
        {
          role: 'assistant',
          content: previousResponse
        }
      ];

      return await this.continueWithToolResultsUsingMessages(
        messages,
        toolCalls,
        toolCallResults,
        options
      );
    } catch (error) {
      console.error('继续对话错误:', error);
      throw error;
    }
  }
}
