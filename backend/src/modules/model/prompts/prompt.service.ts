/**
 * 主提示词服务
 * 整合所有提示词服务，提供统一的接口
 */
import { BasePromptService } from './base-prompt.service';
import { SystemPromptService } from './system-prompt.service';
import { MemoryPromptService } from './memory-prompt.service';
import { ThinkingPromptService } from './thinking-prompt.service';
import { ToolPromptService } from './tool-prompt.service';

export class PromptService {
  constructor(
    private basePromptService: BasePromptService,
    private systemPromptService: SystemPromptService,
    private memoryPromptService: MemoryPromptService,
    private thinkingPromptService: ThinkingPromptService,
    private toolPromptService: ToolPromptService
  ) {}

  // 系统提示词相关方法
  getSystemPrompt(): string {
    return this.systemPromptService.getSystemPrompt();
  }

  saveSystemPrompt(prompt: string): boolean {
    return this.systemPromptService.saveSystemPrompt(prompt);
  }

  getUniversalSystemPrompt(): string {
    return this.systemPromptService.getUniversalSystemPrompt();
  }

  getToolSystemPrompt(): string {
    return this.systemPromptService.getToolSystemPrompt();
  }

  // 工具提示词相关方法
  getToolsPrompt(tools: any[], enableSearch: boolean = false): string {
    return this.toolPromptService.getToolsPrompt(tools, enableSearch);
  }

  getToolResultsPrompt(toolCalls: any[], toolCallResults: any[], format: 'traditional' | 'openai' = 'traditional'): string {
    return this.toolPromptService.getToolResultsPrompt(toolCalls, toolCallResults, format);
  }

  getToolResultsSystemPrompt(): string {
    return this.toolPromptService.getToolResultsPrompt([], [], 'openai');
  }

  // 记忆提示词相关方法
  getMemoriesPrompt(memories: any[]): string {
    return this.memoryPromptService.getMemoriesPrompt(memories);
  }

  getMemoryGenerationPrompt(context: string, type: 'general' | 'reflection' | 'multiple' = 'general'): string {
    return this.memoryPromptService.getMemoryGenerationPrompt(context, type);
  }

  getReflectionMemoryPrompt(conversation: string): string {
    return this.memoryPromptService.getMemoryGenerationPrompt(conversation, 'reflection');
  }

  getMultipleMemoriesPrompt(conversation: string): string {
    return this.memoryPromptService.getMemoryGenerationPrompt(conversation, 'multiple');
  }

  getMemoryConflictAnalysisPrompt(memories: any[]): string {
    return this.memoryPromptService.getMemoryConflictAnalysisPrompt(memories);
  }

  getMemoryReviewPrompt(memories: any[], context: string): string {
    return this.memoryPromptService.getMemoryReviewPrompt(memories, context);
  }

  // 思考提示词相关方法
  getDeepThinkingPrompt(question: string, tools: any[] = []): string {
    return this.thinkingPromptService.getDeepThinkingPrompt(question, tools);
  }

  getDeepThinkingWithToolsPrompt(question: string, tools: any[]): string {
    return this.thinkingPromptService.getDeepThinkingPrompt(question, tools);
  }

  getThinkingPlanPrompt(question: string): string {
    return this.thinkingPromptService.getThinkingPlanPrompt(question);
  }

  getThinkingStepPrompt(
    question: string,
    stepTitle: string,
    previousSteps: any[] = [],
    userAnswer: string | null = null,
    stepHistory: string | null = null,
    options: any = {}
  ): string {
    return this.thinkingPromptService.getThinkingStepPrompt(
      question,
      stepTitle,
      previousSteps,
      userAnswer,
      stepHistory,
      options
    );
  }

  getThinkingStepContinuePrompt(
    question: string,
    stepTitle: string,
    stepContent: string,
    toolResultsText: string,
    stepHistory: string | null = null,
    options: any = {}
  ): string {
    return this.thinkingPromptService.getThinkingStepContinuePrompt(
      question,
      stepTitle,
      stepContent,
      toolResultsText,
      stepHistory,
      options
    );
  }

  getToolContinueThinkingPrompt(
    question: string,
    stepTitle: string,
    fullStepHistoryText: string
  ): string {
    // 使用合并后的方法，传递适当的参数
    return this.thinkingPromptService.getThinkingStepContinuePrompt(
      question,
      stepTitle,
      '', // 没有步骤内容
      '', // 没有工具结果
      fullStepHistoryText,
      {}
    );
  }

  getToolFailureHandlingPrompt(
    question: string,
    stepTitle: string,
    stepContent: string,
    toolResultsText: string,
    stepHistory: string | null = null
  ): string {
    return this.thinkingPromptService.getToolFailureHandlingPrompt(
      question,
      stepTitle,
      stepContent,
      toolResultsText,
      stepHistory
    );
  }

  getFinalAnswerPrompt(question: string, steps: any[]): string {
    return this.thinkingPromptService.getFinalAnswerPrompt(question, steps);
  }

  /**
   * 获取工具调用重试提示词
   * @param question 原始问题
   * @param stepTitle 步骤标题
   * @param stepContent 步骤内容
   * @param errorMessage 错误信息
   * @param stepHistory 当前步骤的完整对话历史
   * @returns 工具调用重试提示词
   */
  getToolRetryPrompt(
    question: string,
    stepTitle: string,
    stepContent: string,
    errorMessage: string,
    stepHistory: string | null = null
  ): string {
    return this.thinkingPromptService.getToolRetryPrompt(
      question,
      stepTitle,
      stepContent,
      errorMessage,
      stepHistory
    );
  }

  // 图表生成相关方法
  getChartPromptSection(): string {
    return this.basePromptService.getChartPromptSection();
  }

  getDataVisualizationGuideSection(): string {
    return this.basePromptService.getDataVisualizationGuideSection();
  }

  getJsonReturnFormatSection(): string {
    return this.basePromptService.getJsonReturnFormatSection();
  }

  buildToolsAsFunctions(tools: any[]): string {
    return this.basePromptService.buildToolsAsFunctions(tools);
  }

  getCategoryDisplayName(category: string): string {
    return this.basePromptService.getCategoryDisplayName(category);
  }
}
