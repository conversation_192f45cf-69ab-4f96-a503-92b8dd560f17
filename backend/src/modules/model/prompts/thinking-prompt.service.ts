/**
 * 思考提示词服务
 * 管理深度思考相关的提示词
 */
import { BasePromptService } from './base-prompt.service';

export class ThinkingPromptService {
  private basePromptService = new BasePromptService();

  /**
   * 获取深度思考提示词
   * 整合了原来的getDeepThinkingPrompt和getDeepThinkingWithToolsPrompt
   * @param question 问题
   * @param tools 可用工具列表（可选）
   * @returns 深度思考提示词
   */
  getDeepThinkingPrompt(question: string, tools: any[] = []): string {
    // 如果提供了工具，使用带工具的深度思考提示词
    if (tools && tools.length > 0) {
      return this.getDeepThinkingWithToolsPrompt(question, tools);
    }

    // 否则使用基本的深度思考提示词
    return `你是Sparkle的深度思考引擎，解决复杂问题。系统分析问题，分解为逻辑步骤，得出全面结论。

问题:${question}

思考框架:问题分析→步骤设计→系统执行→综合结论

步骤原则:逻辑递进；独立完整；全面覆盖；适度精简

思考策略:
-概念问题:定义→分解→关联→应用→综合
-技术问题:定义→分析→方案→评估→建议
-决策问题:选项→标准→比较→权衡→推荐
-创造问题:发散→评估→构建→优化→创意

返回JSON格式:
{
  "steps": [
    {
      "title": "步骤1:[标题]",
      "content": "思考过程，包括分析推理和评估",
      "conclusion": "步骤结论，作为后续基础"
    },
    {
      "title": "步骤2:[标题]",
      "content": "基于前步骤的深入分析",
      "conclusion": "进一步推进的结论"
    }
  ],
  "final_answer": "综合全部步骤的答案"
}

质量要素:结构清晰；逻辑严谨；证据充分；多角度思考；结论明确

只返回JSON格式结果，不要其他文字。`;
  }

  /**
   * 获取带工具的深度思考提示词
   * @param question 问题
   * @param tools 可用工具列表
   * @returns 带工具的深度思考提示词
   */
  private getDeepThinkingWithToolsPrompt(question: string, tools: any[]): string {
    // 构建包含工具描述的提示词
    const toolsDescription = tools.map(tool =>
      `工具名称: ${tool.name}\n描述: ${tool.description}\n输入格式: ${JSON.stringify(tool.input_schema, null, 2)}`
    ).join('\n\n');

    return `
你是Sparkle的深度思考引擎，可以使用工具来帮助思考。请对以下问题进行深入分析，并将思考过程分解为多个步骤。
对于每个步骤，你可以使用工具来获取信息，然后提供详细的思考过程和结论。

问题: ${question}

可用工具:
${toolsDescription}

你还可以使用两种方式生成图表：

1. 使用 Mermaid 生成流程图、时序图、甘特图等：
   适用场景：流程图、时序图、状态图、类图、实体关系图等结构化图表

   示例：
   \`\`\`mermaid
   graph TD
       A[开始] --> B[处理]
       B --> C{判断}
       C -->|是| D[处理1]
       C -->|否| E[处理2]
       D --> F[结束]
       E --> F
   \`\`\`

2. 使用 ECharts 生成数据可视化图表：
   适用场景：柱状图、折线图、饼图、散点图等数据可视化图表

   示例：
   \`\`\`echarts
   {
     "title": {
       "text": "示例图表"
     },
     "tooltip": {},
     "xAxis": {
       "data": ["衬衫", "羊毛衫", "雪纺衫", "裤子", "高跟鞋", "袜子"]
     },
     "yAxis": {},
     "series": [{
       "name": "销量",
       "type": "bar",
       "data": [5, 20, 36, 10, 10, 20]
     }]
   }
   \`\`\`

   注意：ECharts配置必须是纯JSON格式，不支持JavaScript函数。使用字符串模板如"{b}: {c}分"代替函数，使用visualMap或固定颜色数组代替动态颜色函数。

请按照以下JSON格式返回你的思考步骤:

{
  "steps": [
    {
      "title": "步骤1标题",
      "content": "步骤1的详细思考过程",
      "tool_calls": [
        {
          "name": "工具名称",
          "input": {
            // 工具输入参数
          }
        }
      ],
      "conclusion": "步骤1的结论"
    },
    // 更多步骤...
  ],
  "final_answer": "最终综合所有步骤得出的答案"
}

请只返回JSON格式的结果，不要有其他文字。
`;
  }

  /**
   * 获取深度思考步骤规划提示词
   * @param question 问题
   * @returns 深度思考步骤规划提示词
   */
  getThinkingPlanPrompt(question: string): string {
    return `你是Sparkle的思考规划引擎，将复杂问题分解为精确可执行的思考步骤。

问题:${question}

规划框架:问题解构→信息需求→步骤设计→资源规划→依赖管理

步骤标准:目标明确；自包含；资源高效；逻辑连贯；全面覆盖

可用工具:
-信息获取:safe_shell执行文件读写搜索
-数据处理:safe_shell处理分析数据
-可视化:Mermaid/ECharts生成图表
-用户交互:必要时提问获取信息

返回JSON格式:
{
  "steps": [
    {
      "title": "步骤1:[标题]",
      "objective": "目标、预期输出和关系。包括:1)核心问题 2)所需工具 3)预期结果 4)步骤关系"
    },
    {
      "title": "步骤2:[标题]",
      "objective": "目标、如何利用前步骤结果、如何推进整体解决方案"
    }
  ]
}

规划策略:
-信息型:获取→验证→分析→综合→结论
-分析型:分解→因素分析→关系探索→模式识别→评估
-决策型:选项→标准→评估→权衡→选择
-创造型:定义→发散→构建→评估→优化

原则:精简步骤；前置依赖；资源优化；可执行性；适应性

只返回JSON格式结果，无其他文字。`;
  }

  /**
   * 获取思考步骤执行提示词
   * @param question 原始问题
   * @param stepTitle 步骤标题
   * @param previousSteps 之前步骤的结果
   * @param userAnswer 用户对上一个问题的回答
   * @param stepHistory 当前步骤的完整对话历史
   * @param options 生成选项
   * @returns 思考步骤执行提示词
   */
  getThinkingStepPrompt(
    question: string,
    stepTitle: string,
    previousSteps: any[] = [],
    userAnswer: string | null = null,
    stepHistory: string | null = null,
    options: any = {}
  ): string {
    // 构建之前步骤结果摘要
    let previousStepsText = '';
    if (previousSteps.length > 0) {
      previousStepsText = '之前步骤结果:\n';
      previousSteps.forEach((step, index) => {
        previousStepsText += `步骤${index+1}:${step.title}\n结论:${step.conclusion}\n`;
      });
    }

    // 构建其他文本
    let stepHistoryText = stepHistory ? `之前思考:\n${stepHistory}\n\n基于上述继续完成。` : '';
    let userAnswerText = userAnswer ? `用户回答:${userAnswer}\n\n根据用户信息继续。` : '';

    // 构建工具文本
    let toolsText = '';
    if (options.tools && options.tools.length > 0) {
      toolsText = '可用工具:\n';
      options.tools.forEach((tool: any) => {
        toolsText += `工具:${tool.name}\n描述:${tool.description}\n输入:${JSON.stringify(tool.input_schema)}\n`;
      });
    }

    return `你是Sparkle深度思考引擎，执行复杂问题分析步骤。专注完成当前步骤目标，提供深入分析和明确结论。

原始问题:${question}
当前步骤:${stepTitle}
步骤目标:${options.currentStep ? options.currentStep.objective : '分析解决当前步骤问题'}

${previousStepsText}
${stepHistoryText}
${userAnswerText}
${toolsText}

执行指南:专注当前目标；提供结构化思考；明确工具选择理由；提问要具体相关；结论要直接回应目标。

重要:步骤目标含"用户反馈/确认/选择"等关键词时，必须提问等待回答，不能跳过用户交互。

${this.basePromptService.getChartPromptSection()}

执行原则:一次获取所需信息；自主决策(目标要求用户参与除外)；步骤内完成所有任务；精准使用工具；直接生成结果；适当使用图表。

用户交互:目标要求用户参与时用ask_user格式；明确告知决策需求；用户回答"继续"立即完成；基于已有信息完成避免重复提问。

工具使用:文件操作通过工具执行；调用前说明目的；立即分析结果；失败时分析原因尝试替代方案。

提问JSON格式:
{
  "title": "${stepTitle}",
  "content": "思考过程，说明为何提问",
  "ask_user": true,
  "question": "明确具体的问题"
}

工具调用JSON格式:
{
  "title": "${stepTitle}",
  "content": "思考过程，说明为何用工具",
  "use_tools": true,
  "tool_calls": [
    {
      "name": "工具名称",
      "input": {/*符合schema的参数*/}
    }
  ]
}

结论JSON格式:
{
  "title": "${stepTitle}",
  "content": "思考过程，包括分析推理",
  "images": ["图片URL1"], /*可选*/
  "conclusion": "全面明确结论，包含所有关键发现，自包含不依赖思考过程细节"
}

结论标准:完整包含重要信息；精确准确；直接回应目标；自包含易理解；为后续提供基础。

确保回答为完整JSON格式，无其他文字。`;
  }

  /**
   * 获取思考步骤工具结果处理提示词
   * 整合了原来的getThinkingStepContinuePrompt和getToolContinueThinkingPrompt
   * @param question 原始问题
   * @param stepTitle 步骤标题
   * @param stepContent 步骤内容
   * @param toolResultsText 工具结果文本
   * @param stepHistory 当前步骤的完整对话历史
   * @param options 生成选项
   * @returns 思考步骤工具结果处理提示词
   */
  getThinkingStepContinuePrompt(
    question: string,
    stepTitle: string,
    stepContent: string,
    toolResultsText: string,
    stepHistory: string | null = null,
    options: any = {}
  ): string {
    return `你是Sparkle深度思考引擎，刚使用工具获取信息，现在高效处理结果完成当前步骤。

原始问题:${question}
当前步骤:${stepTitle}
步骤目标:${options.currentStep ? options.currentStep.objective : '分析解决当前步骤问题'}

${stepHistory ? `之前思考:\n${stepHistory}\n\n` : ''}
你之前思考:${stepContent}

工具结果:
${toolResultsText}

处理框架:提取关键信息→评估结果质量→整合分析→得出结论

决策指南:
-结果足够→直接完成步骤给出结论
-部分有用→用已有信息做最佳判断，避免再次调用工具
-调用失败→说明原因，提出替代方案或基于已有信息给结论

${this.basePromptService.getChartPromptSection()}

执行原则:单步完成；结果导向；信息整合；自主决策；适当使用图表

提问JSON格式(仅绝对必要时):
{
  "title": "${stepTitle}",
  "content": "分析工具结果，说明为何必须提问",
  "ask_user": true,
  "question": "明确具体的问题"
}

工具调用JSON格式(需要更多信息时):
{
  "title": "${stepTitle}",
  "content": "思考过程，分析已有结果，解释为何需要更多工具",
  "use_tools": true,
  "tool_calls": [
    {
      "name": "工具名称",
      "input": {/*工具参数*/}
    }
  ]
}

结论JSON格式(大多数情况):
{
  "title": "${stepTitle}",
  "content": "分析工具结果及与目标关联",
  "images": ["图片URL"], /*可选*/
  "conclusion": "全面结论，包含:1)所有关键信息发现 2)信息对目标的意义 3)明确结论陈述。结论应完整自包含，后续步骤可仅基于此继续分析"
}

结论检查:完整包含关键信息；直接回应目标；独立理解不依赖思考过程；信息准确；为后续提供基础

确保回答为完整JSON格式，无其他文字。`;
  }

  /**
   * 获取工具调用失败处理提示词
   * @param question 原始问题
   * @param stepTitle 步骤标题
   * @param stepContent 步骤内容
   * @param toolResultsText 工具结果文本
   * @param stepHistory 当前步骤的完整对话历史
   * @returns 工具调用失败处理提示词
   */
  getToolFailureHandlingPrompt(
    question: string,
    stepTitle: string,
    stepContent: string,
    toolResultsText: string,
    stepHistory: string | null = null
  ): string {
    return `你是Sparkle深度思考引擎，工具调用失败，需快速决定:调整参数重试、使用替代工具或放弃使用工具。

原始问题:${question}
当前步骤:${stepTitle}
${stepHistory ? stepHistory : ''}
你之前思考:${stepContent}

工具结果:
${toolResultsText}

错误处理框架:诊断错误→评估重试价值→考虑替代方案→权衡放弃成本

重要:避免无限重试循环！错误难修复或已尝试类似方法时，应放弃使用工具，直接基于已有信息完成。

错误类型指南:
-语法/参数错误:明确易修复可尝试一次调整
-路径/文件不存在:确认路径正确但文件不存在应放弃
-权限问题:立即放弃，用替代方法或直接完成
-网络/连接问题:可尝试一次简化请求，仍失败则放弃
-功能限制:工具本身无法完成任务立即放弃

决策路径:
1.错误可修复且价值高→调整参数重试
2.工具不适合但有替代→使用替代工具
3.价值不高或多次失败→放弃使用工具直接完成

重试JSON格式(错误可修复且价值高):
{
  "title": "${stepTitle}",
  "content": "错误分析和修正策略",
  "use_tools": true,
  "tool_calls": [
    {
      "name": "工具名称",
      "input": {/*调整后参数*/}
    }
  ]
}

替代工具JSON格式(有合适替代):
{
  "title": "${stepTitle}",
  "content": "错误分析和替代策略",
  "use_tools": true,
  "tool_calls": [
    {
      "name": "替代工具名称",
      "input": {/*替代工具参数*/}
    }
  ]
}

放弃工具JSON格式(错误难修复或价值不高):
{
  "title": "${stepTitle}",
  "content": "错误分析和放弃原因",
  "conclusion": "基于可用信息的结论，包含:1)失败原因简述 2)基于现有信息的关键发现 3)明确结论陈述。结论应完整自包含，提供有价值信息。"
}

失败后结论指南:清晰说明失败但不过度强调；充分利用已有信息；使用逻辑推理；确保结论涵盖关键方面；标记不确定内容

提问JSON格式(仅其他策略都不可行时):
{
  "title": "${stepTitle}",
  "content": "错误分析和尝试过的解决方案",
  "ask_user": true,
  "question": "明确具体的问题"
}

只返回JSON格式结果，无其他文字。`;
  }

  /**
   * 获取最终答案生成提示词
   * @param question 原始问题
   * @param steps 所有步骤的结果
   * @returns 最终答案生成提示词
   */
  getFinalAnswerPrompt(question: string, steps: any[]): string {
    // 构建步骤结果摘要
    let stepsText = '';
    if (steps.length > 0) {
      stepsText = '思考步骤结果:\n';
      steps.forEach((step, index) => {
        stepsText += `步骤${index+1}:${step.title}\n结论:${step.conclusion}\n`;

        // 添加图片
        if (step.images && step.images.length > 0) {
          stepsText += `图片:`;
          step.images.forEach((imageUrl: string, imgIndex: number) => {
            stepsText += `![图片${imgIndex+1}](${imageUrl})\n`;
          });
        }
      });
    }

    return `你是Sparkle深度思考引擎，已完成多步分析。整合所有步骤结论，生成全面连贯直接回应原问题的最终答案。

原始问题:${question}

${stepsText}

答案框架:核心回应(直接回答问题)；关键发现(整合重要结论)；逻辑连贯(内部一致)；完整自包含(独立理解)

整合指南:重要信息优先；避免重复分析；处理冲突；添加连接内容；优化结构提高可读性

可视化策略:引用步骤图片；数据用表格/ECharts；关系用Mermaid；合理安排位置

${this.basePromptService.getChartPromptSection()}

图片处理:只用已有图片链接；在相关上下文嵌入；格式正确![标题](URL)；添加描述性标题

质量标准:直接回应问题；涵盖所有重要方面；陈述准确；提供明确建议；使用清晰语言结构

生成专业全面用户友好的最终答案，独立完整回应原始问题。`;
  }

  /**
   * 获取工具调用重试提示词
   * @param question 原始问题
   * @param stepTitle 步骤标题
   * @param stepContent 步骤内容
   * @param errorMessage 错误信息
   * @param stepHistory 当前步骤的完整对话历史
   * @returns 工具调用重试提示词
   */
  getToolRetryPrompt(
    question: string,
    stepTitle: string,
    stepContent: string,
    errorMessage: string,
    stepHistory: string | null = null
  ): string {
    return `你是Sparkle深度思考引擎，工具使用遇错误，需快速决定:调整参数重试、使用替代工具或放弃工具。

原始问题:${question}
当前步骤:${stepTitle}
${stepHistory ? stepHistory : ''}
你之前思考:${stepContent}
工具错误:${errorMessage}

错误修复框架:评估错误严重性→考虑重试次数→评估替代选项→权衡放弃成本

重要:避免无限重试循环！同一工具失败2次以上必须放弃，直接基于已有信息完成。

错误应对策略:
-语法/参数错误:尝试一次精确修正
-路径/文件不存在:确认路径正确但文件不存在应放弃
-权限问题:立即放弃，用替代方法或直接完成
-网络/连接问题:可尝试一次简化请求，仍失败则放弃
-功能限制:工具本身无法完成任务立即放弃

决策路径:
1.首次失败且错误可修复→调整参数重试
2.首次失败但错误难修复→尝试替代工具
3.多次失败或无合适替代→放弃工具直接完成

重试JSON格式(首次失败且错误可修复):
{
  "title": "${stepTitle}",
  "content": "错误分析和修正策略",
  "use_tools": true,
  "tool_calls": [
    {
      "name": "工具名称",
      "input": {/*调整后参数*/}
    }
  ]
}

替代工具JSON格式(首次失败错误难修复):
{
  "title": "${stepTitle}",
  "content": "错误分析和替代策略",
  "use_tools": true,
  "tool_calls": [
    {
      "name": "替代工具名称",
      "input": {/*替代工具参数*/}
    }
  ]
}

放弃工具JSON格式(多次失败或无合适替代):
{
  "title": "${stepTitle}",
  "content": "错误分析和放弃原因",
  "conclusion": "基于已有信息的最佳结论，明确说明因工具调用失败结论可能不完整，但尽可能提供有价值信息。"
}

只返回JSON格式结果，无其他文字。`;
  }
}
