import Database from 'better-sqlite3';
import path from 'path';
import fs from 'fs';
import dotenv from 'dotenv';
import { DEFAULT_MODEL_CONFIG } from './model.config';

dotenv.config();

// 获取数据库路径
const dbPath = process.env.DB_PATH || './data/sparkle.db';

// 确保数据库目录存在
const dbDir = path.dirname(dbPath);
if (!fs.existsSync(dbDir)) {
  fs.mkdirSync(dbDir, { recursive: true });
}

// 创建数据库连接
const db = new Database(dbPath);

// 启用外键约束
db.pragma('foreign_keys = ON');

// 初始化数据库表
function initDatabase() {
  // 创建记忆表
  db.exec(`
    CREATE TABLE IF NOT EXISTS memories (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      timestamp INTEGER NOT NULL,
      keywords TEXT NOT NULL,
      context TEXT NOT NULL,
      content TEXT NOT NULL,
      importance REAL DEFAULT 1.0,
      last_accessed INTEGER,
      created_at INTEGER NOT NULL
    );
  `);

  // 创建关联记忆表
  db.exec(`
    CREATE TABLE IF NOT EXISTS memory_relations (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      memory_id INTEGER NOT NULL,
      related_memory_id INTEGER NOT NULL,
      relation_strength REAL DEFAULT 1.0,
      created_at INTEGER NOT NULL,
      FOREIGN KEY (memory_id) REFERENCES memories(id) ON DELETE CASCADE,
      FOREIGN KEY (related_memory_id) REFERENCES memories(id) ON DELETE CASCADE
    );
  `);

  // 创建记忆复习表
  db.exec(`
    CREATE TABLE IF NOT EXISTS memory_reviews (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      memory_id INTEGER NOT NULL,
      review_time INTEGER NOT NULL,
      FOREIGN KEY (memory_id) REFERENCES memories(id) ON DELETE CASCADE
    );
  `);

  // 创建对话会话表
  db.exec(`
    CREATE TABLE IF NOT EXISTS conversations (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      title TEXT NOT NULL,
      created_at INTEGER NOT NULL,
      updated_at INTEGER NOT NULL,
      is_active BOOLEAN DEFAULT 1
    );
  `);

  // 创建对话消息表
  db.exec(`
    CREATE TABLE IF NOT EXISTS conversation_messages (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      conversation_id INTEGER NOT NULL,
      content TEXT NOT NULL,
      sender TEXT NOT NULL,
      timestamp INTEGER NOT NULL,
      tool_calls TEXT,
      tool_results TEXT,
      FOREIGN KEY (conversation_id) REFERENCES conversations(id) ON DELETE CASCADE
    );
  `);

  // 创建设置表
  db.exec(`
    CREATE TABLE IF NOT EXISTS settings (
      key TEXT PRIMARY KEY,
      value TEXT NOT NULL,
      updated_at INTEGER NOT NULL
    );
  `);

  // 创建快照表
  db.exec(`
    CREATE TABLE IF NOT EXISTS snapshots (
      id TEXT PRIMARY KEY,
      operation_id TEXT NOT NULL,
      timestamp INTEGER NOT NULL,
      expires_at INTEGER NOT NULL
    );
  `);

  // 创建审计日志表
  db.exec(`
    CREATE TABLE IF NOT EXISTS audit_logs (
      id TEXT PRIMARY KEY,
      user_id TEXT NOT NULL,
      operation_id TEXT NOT NULL,
      operation_type TEXT NOT NULL,
      risk_level TEXT NOT NULL,
      success INTEGER NOT NULL,
      error TEXT,
      snapshot_id TEXT,
      timestamp INTEGER NOT NULL,
      details TEXT NOT NULL
    );
  `);

  // 创建用户决策表
  db.exec(`
    CREATE TABLE IF NOT EXISTS user_decisions (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      user_id TEXT NOT NULL,
      operation_type TEXT NOT NULL,
      risk_level TEXT NOT NULL,
      decision TEXT NOT NULL,
      timestamp INTEGER NOT NULL
    );
  `);

  // 初始化默认设置
  const timestamp = Date.now();
  const defaultSettings = [
    { key: 'model_provider', value: DEFAULT_MODEL_CONFIG.DEFAULT_PROVIDER, updated_at: timestamp },
    { key: 'ollama_api_url', value: process.env.OLLAMA_API_URL || DEFAULT_MODEL_CONFIG.OLLAMA.API_URL, updated_at: timestamp },
    { key: 'ollama_model', value: DEFAULT_MODEL_CONFIG.OLLAMA.MODEL, updated_at: timestamp },
    { key: 'qwen_api_url', value: process.env.QWEN_API_URL || DEFAULT_MODEL_CONFIG.QWEN.API_URL, updated_at: timestamp },
    { key: 'qwen_api_key', value: process.env.QWEN_API_KEY || '', updated_at: timestamp },
    { key: 'qwen_model', value: DEFAULT_MODEL_CONFIG.QWEN.MODEL, updated_at: timestamp },
    { key: 'deepseek_api_url', value: process.env.DEEPSEEK_API_URL || DEFAULT_MODEL_CONFIG.DEEPSEEK.API_URL, updated_at: timestamp },
    { key: 'deepseek_api_key', value: process.env.DEEPSEEK_API_KEY || '', updated_at: timestamp },
    { key: 'deepseek_model', value: DEFAULT_MODEL_CONFIG.DEEPSEEK.MODEL, updated_at: timestamp },
    { key: 'temperature', value: DEFAULT_MODEL_CONFIG.PARAMETERS.TEMPERATURE.toString(), updated_at: timestamp },
    { key: 'max_tokens', value: DEFAULT_MODEL_CONFIG.PARAMETERS.MAX_TOKENS.toString(), updated_at: timestamp },
    { key: 'enable_memory', value: 'true', updated_at: timestamp },
    { key: 'memory_importance_threshold', value: '0.5', updated_at: timestamp },
    { key: 'enable_deep_thinking', value: 'true', updated_at: timestamp }
  ];

  // 使用事务批量插入默认设置
  const insertSetting = db.prepare(`
    INSERT OR IGNORE INTO settings (key, value, updated_at)
    VALUES (?, ?, ?)
  `);

  // 检查设置表是否为空（首次初始化）
  const settingsCount = db.prepare('SELECT COUNT(*) as count FROM settings').get() as { count: number };
  const isFirstInit = settingsCount.count === 0;

  db.transaction(() => {
    for (const setting of defaultSettings) {
      // 只在首次初始化时插入默认设置
      insertSetting.run(setting.key, setting.value, setting.updated_at);

      // 只在首次初始化时，如果环境变量中有设置，则使用环境变量的值
      if (isFirstInit) {
        if (setting.key === 'ollama_api_url' && process.env.OLLAMA_API_URL) {
          db.prepare('UPDATE settings SET value = ?, updated_at = ? WHERE key = ?')
            .run(process.env.OLLAMA_API_URL, timestamp, setting.key);
        } else if (setting.key === 'qwen_api_url' && process.env.QWEN_API_URL) {
          db.prepare('UPDATE settings SET value = ?, updated_at = ? WHERE key = ?')
            .run(process.env.QWEN_API_URL, timestamp, setting.key);
        } else if (setting.key === 'qwen_api_key' && process.env.QWEN_API_KEY) {
          db.prepare('UPDATE settings SET value = ?, updated_at = ? WHERE key = ?')
            .run(process.env.QWEN_API_KEY, timestamp, setting.key);
        } else if (setting.key === 'deepseek_api_url' && process.env.DEEPSEEK_API_URL) {
          db.prepare('UPDATE settings SET value = ?, updated_at = ? WHERE key = ?')
            .run(process.env.DEEPSEEK_API_URL, timestamp, setting.key);
        } else if (setting.key === 'deepseek_api_key' && process.env.DEEPSEEK_API_KEY) {
          db.prepare('UPDATE settings SET value = ?, updated_at = ? WHERE key = ?')
            .run(process.env.DEEPSEEK_API_KEY, timestamp, setting.key);
        } else if (setting.key === 'model_provider' && process.env.MODEL_PROVIDER) {
          db.prepare('UPDATE settings SET value = ?, updated_at = ? WHERE key = ?')
            .run(process.env.MODEL_PROVIDER, timestamp, setting.key);
        }
      }
    }
  })();

  console.log('数据库初始化完成');
}

export { db, initDatabase };
