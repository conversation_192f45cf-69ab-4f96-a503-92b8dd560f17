{
  "name": "backend",
  "version": "1.0.0",
  "description": "",
  "main": "index.js",
  "scripts": {
    "clean": "rimraf dist",
    "prebuild": "npm run clean",
    "build": "tsc",
    "postbuild": "copyfiles -u 1 src/**/*.json dist/",
    "start": "node dist/index.js",
    "start:prod": "NODE_ENV=production node dist/index.js",
    "dev": "ts-node-dev --respawn --transpile-only src/index.ts",
    "dev:debug": "ts-node-dev --respawn --transpile-only --inspect -- src/index.ts",
    "lint": "eslint 'src/**/*.{ts,js}'",
    "lint:fix": "eslint 'src/**/*.{ts,js}' --fix",
    "migrate": "ts-node src/migrate.ts",

  },
  "keywords": [],
  "author": "",
  "license": "ISC",
  "dependencies": {
    "@mozilla/readability": "^0.6.0",
    "@types/bcrypt": "^5.0.2",
    "@types/express": "^4.17.21",
    "@types/jsonwebtoken": "^9.0.9",
    "@types/node": "^22.15.2",
    "@types/swagger-jsdoc": "^6.0.4",
    "@types/swagger-ui-express": "^4.1.8",
    "@xenova/transformers": "^2.17.2",
    "axios": "^1.9.0",
    "bcrypt": "^5.1.1",
    "better-sqlite3": "^11.9.1",
    "cheerio": "^1.0.0-rc.12",
    "compression": "^1.8.0",
    "cors": "^2.8.5",
    "dotenv": "^16.5.0",
    "express": "^4.18.2",
    "hnswlib-node": "^3.0.0",
    "html-to-text": "^9.0.5",
    "jsdom": "^26.1.0",
    "jsonwebtoken": "^9.0.2",
    "socket.io": "^4.8.1",
    "swagger-jsdoc": "^6.2.8",
    "swagger-ui-express": "^5.0.1",
    "ts-node": "^10.9.2",
    "tslib": "^2.8.1",
    "typescript": "^5.8.3",
    "uuid": "^11.1.0"
  },
  "devDependencies": {
    "@types/better-sqlite3": "^7.6.13",
    "@types/compression": "^1.7.5",
    "@types/html-to-text": "^9.0.4",

    "@types/jsdom": "^21.1.7",
    "@types/node-fetch": "^2.6.12",

    "@types/uuid": "^10.0.0",
    "@typescript-eslint/eslint-plugin": "^7.0.0",
    "@typescript-eslint/parser": "^7.0.0",
    "copyfiles": "^2.4.1",
    "eslint": "^8.56.0",

    "rimraf": "^5.0.10",

    "ts-node-dev": "^2.0.0"
  }
}
