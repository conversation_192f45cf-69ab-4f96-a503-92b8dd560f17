/**
 * 测试safe_shell工具获取系统信息
 */
const { toolService } = require('./dist/modules/tools');

async function testSystemInfo() {
  try {
    console.log('测试safe_shell工具获取系统信息...');
    
    // 初始化工具服务
    await toolService.initialize();
    
    // 获取系统信息
    const commands = [
      { name: 'CPU信息', cmd: 'cat /proc/cpuinfo | grep "model name" | head -n 1' },
      { name: '内存信息', cmd: 'free -h' },
      { name: '磁盘使用情况', cmd: 'df -h' },
      { name: '系统版本', cmd: 'cat /etc/os-release | grep PRETTY_NAME' },
      { name: '当前进程', cmd: 'ps aux | head -n 5' }
    ];
    
    for (const command of commands) {
      console.log(`\n获取${command.name}...`);
      const result = await toolService.executeTool('safe_shell', { 
        command: command.cmd,
        risk_level: 'low'
      });
      
      if (result.tool_output.success) {
        console.log(`${command.name}:\n${result.tool_output.stdout}`);
      } else {
        console.error(`获取${command.name}失败:`, result.tool_output.error || result.tool_output.stderr);
      }
    }
    
    console.log('\n测试完成');
  } catch (error) {
    console.error('测试失败:', error);
  }
}

// 执行测试
testSystemInfo();
