# 依赖目录
node_modules/

# 编译后的文件
dist/
build/
**/dist/**/*.js
**/dist/**/*.js.map
**/build/**/*.js
**/build/**/*.js.map
# 源代码目录中的编译后文件
backend/src/**/*.js
backend/src/**/*.js.map


# 日志文件
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

# 编辑器目录和文件
.vscode/*
!.vscode/extensions.json
.idea/
.DS_Store
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?

# 环境变量文件
.env
.env.local
.env.development.local

.env.production.local

# 数据库文件
*.sqlite
*.sqlite3
*.db

# 缓存
.npm
.eslintcache
.stylelintcache
.rpt2_cache/
.rts2_cache_cjs/
.rts2_cache_es/
.rts2_cache_umd/



# 模型文件
backend/models/

# bing_search_result.html 文件
backend/bing_search_result.html
bing_search_result.html

/releases/*