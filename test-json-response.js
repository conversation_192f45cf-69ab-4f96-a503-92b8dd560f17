/**
 * 测试JSON响应处理
 */
const { modelService } = require('./dist/modules/model');

async function testJsonResponse() {
  try {
    console.log('测试JSON响应处理...');
    
    // 模拟大模型返回JSON格式的响应
    const jsonResponse = `{ "thoughts": "用户说'你做的很棒！'，这表明用户对我的回答满意，不需要进一步的操作或工具使用。", "tool_calls": [] }`;
    
    // 获取模型提供商实例
    const modelProvider = modelService.getModelProviderInstance();
    
    // 模拟解析JSON响应
    console.log('原始JSON响应:');
    console.log(jsonResponse);
    
    // 尝试解析JSON
    try {
      // 查找JSON部分
      const jsonMatch = jsonResponse.match(/\{[\s\S]*\}/);
      if (jsonMatch) {
        try {
          const parsedResponse = JSON.parse(jsonMatch[0]);
          
          console.log('解析后的JSON:');
          console.log(JSON.stringify(parsedResponse, null, 2));
          
          // 如果有工具调用，返回工具调用结果
          if (parsedResponse.tool_calls && parsedResponse.tool_calls.length > 0) {
            console.log('检测到工具调用:');
            console.log(JSON.stringify(parsedResponse.tool_calls, null, 2));
          }
          
          // 如果有thoughts字段但没有工具调用，只返回thoughts内容
          if (parsedResponse.thoughts && (!parsedResponse.tool_calls || parsedResponse.tool_calls.length === 0)) {
            console.log('检测到thoughts字段，没有工具调用:');
            console.log(parsedResponse.thoughts);
            
            const result = { 
              content: parsedResponse.thoughts,
              toolCalls: [] 
            };
            
            console.log('最终结果:');
            console.log(JSON.stringify(result, null, 2));
          }
        } catch (error) {
          console.error('解析JSON错误:', error);
        }
      }
    } catch (error) {
      console.error('处理JSON响应错误:', error);
    }
    
    console.log('测试完成');
  } catch (error) {
    console.error('测试失败:', error);
  }
}

// 执行测试
testJsonResponse();
