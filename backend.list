# 后端服务模块化重构计划

## 第一阶段：创建基础目录结构

1. [x] 创建新的目录结构
   - 创建 src/modules 目录作为所有模块的根目录
   - 在 modules 下创建各个模块目录

## 第二阶段：模型服务模块重构

2. [x] 创建模型服务模块目录结构
   - 创建 src/modules/model 目录
   - 创建 src/modules/model/providers 目录用于不同模型提供商
   - 创建 src/modules/model/interfaces 目录用于接口定义

3. [x] 重构模型接口
   - 创建 src/modules/model/interfaces/model-provider.interface.ts
   - 创建 src/modules/model/interfaces/model-service.interface.ts

4. [x] 重构模型提供商实现
   - 创建 src/modules/model/providers/ollama.provider.ts
   - 创建 src/modules/model/providers/qwen.provider.ts
   - 创建 src/modules/model/providers/modelscope.provider.ts (暂时跳过)
   - 创建 src/modules/model/providers/modelscope-local.provider.ts (暂时跳过)

5. [x] 重构模型服务
   - 创建 src/modules/model/model.service.ts
   - 创建 src/modules/model/model.config.ts

6. [x] 创建模型模块入口
   - 创建 src/modules/model/index.ts

7. [x] 移除旧的模型服务文件
   - 移除 src/services/modelService.ts
   - 移除 src/services/ollamaService.ts
   - 移除 src/services/qwenService.ts
   - 移除 src/services/modelScopeService.ts
   - 移除 src/services/modelScopeLocalService.ts

## 第三阶段：记忆服务模块重构

8. [x] 创建记忆服务模块目录结构
   - 创建 src/modules/memory 目录
   - 创建 src/modules/memory/interfaces 目录

9. [x] 重构记忆接口和类型
   - 创建 src/modules/memory/interfaces/memory.interface.ts
   - 创建 src/modules/memory/memory.types.ts

10. [x] 重构记忆服务
    - 创建 src/modules/memory/memory.service.ts
    - 创建 src/modules/memory/memory-organization.service.ts

11. [x] 创建记忆模块入口
    - 创建 src/modules/memory/index.ts

12. [x] 移除旧的记忆服务文件
    - 移除 src/services/memoryService.ts
    - 移除 src/services/memoryOrganizationService.ts

## 第四阶段：对话服务模块重构

13. [x] 创建对话服务模块目录结构
    - 创建 src/modules/conversation 目录
    - 创建 src/modules/conversation/interfaces 目录

14. [x] 重构对话接口和类型
    - 创建 src/modules/conversation/interfaces/conversation.interface.ts
    - 创建 src/modules/conversation/conversation.types.ts

15. [x] 重构对话服务
    - 创建 src/modules/conversation/conversation.service.ts

16. [x] 创建对话模块入口
    - 创建 src/modules/conversation/index.ts

17. [x] 移除旧的对话服务文件
    - 移除 src/services/conversationService.ts

## 第五阶段：工具服务模块重构

18. [x] 创建工具服务模块目录结构
    - 创建 src/modules/tools 目录
    - 创建 src/modules/tools/interfaces 目录
    - 创建 src/modules/tools/builtin 目录用于内置工具

19. [x] 重构工具接口和类型
    - 创建 src/modules/tools/interfaces/tool.interface.ts
    - 创建 src/modules/tools/tools.types.ts

20. [x] 重构工具服务
    - 创建 src/modules/tools/tool.service.ts
    - 创建 src/modules/tools/tool.manager.ts

21. [x] 重构内置工具
    - 创建 src/modules/tools/builtin/file.tool.ts
    - 创建 src/modules/tools/builtin/shell.tool.ts
    - 创建 src/modules/tools/builtin/index.ts

22. [x] 创建工具模块入口
    - 创建 src/modules/tools/index.ts

23. [x] 移除旧的工具服务文件
    - 移除 src/services/toolService.ts

## 第六阶段：设置服务模块重构

24. [x] 创建设置服务模块目录结构
    - 创建 src/modules/settings 目录
    - 创建 src/modules/settings/interfaces 目录

25. [x] 重构设置接口和类型
    - 创建 src/modules/settings/interfaces/setting.interface.ts
    - 创建 src/modules/settings/settings.types.ts

26. [x] 重构设置服务
    - 创建 src/modules/settings/settings.service.ts

27. [x] 创建设置模块入口
    - 创建 src/modules/settings/index.ts

28. [x] 移除旧的设置服务文件
    - 移除 src/services/settingService.ts

## 第七阶段：自主任务服务模块重构

29. [x] 创建自主任务服务模块目录结构
    - 创建 src/modules/autonomous-task 目录
    - 创建 src/modules/autonomous-task/interfaces 目录

30. [x] 重构自主任务接口和类型
    - 创建 src/modules/autonomous-task/interfaces/task.interface.ts
    - 创建 src/modules/autonomous-task/autonomous-task.types.ts

31. [x] 重构自主任务服务
    - 创建 src/modules/autonomous-task/autonomous-task.service.ts
    - 创建 src/modules/autonomous-task/task-executor.service.ts

32. [x] 创建自主任务模块入口
    - 创建 src/modules/autonomous-task/index.ts

33. [x] 移除旧的自主任务服务文件
    - 移除 src/services/autonomousTaskService.ts

## 第八阶段：WebSocket服务模块重构

34. [x] 创建WebSocket服务模块目录结构
    - 创建 src/modules/socket 目录
    - 创建 src/modules/socket/handlers 目录用于不同的消息处理器

35. [x] 重构WebSocket接口和类型
    - 创建 src/modules/socket/socket.types.ts

36. [x] 重构WebSocket服务
    - 创建 src/modules/socket/socket.service.ts

37. [x] 重构消息处理器
    - 创建 src/modules/socket/handlers/chat.handler.ts
    - 创建 src/modules/socket/handlers/thinking.handler.ts
    - 创建 src/modules/socket/handlers/tool.handler.ts
    - 创建 src/modules/socket/handlers/memory.handler.ts
    - 创建 src/modules/socket/handlers/conversation.handler.ts
    - 创建 src/modules/socket/handlers/index.ts

38. [x] 创建WebSocket模块入口
    - 创建 src/modules/socket/index.ts

39. [x] 移除旧的WebSocket服务文件
    - 移除 src/services/socketService.ts

## 第九阶段：向量数据库服务模块重构

40. [x] 创建向量数据库服务模块目录结构
    - 创建 src/modules/vector-db 目录

41. [x] 重构向量数据库服务
    - 创建 src/modules/vector-db/vector-db.service.ts

42. [x] 创建向量数据库模块入口
    - 创建 src/modules/vector-db/index.ts

43. [x] 移除旧的向量数据库服务文件
    - 移除 src/services/vectorDbService.ts

## 第十阶段：路由和控制器重构

44. [x] 创建API模块目录结构
    - 创建 src/modules/api 目录
    - 创建 src/modules/api/controllers 目录
    - 创建 src/modules/api/routes 目录

45. [x] 重构控制器
    - 创建 src/modules/api/controllers/model.controller.ts
    - 创建 src/modules/api/controllers/memory.controller.ts
    - 创建 src/modules/api/controllers/conversation.controller.ts
    - 创建 src/modules/api/controllers/settings.controller.ts
    - 创建 src/modules/api/controllers/tools.controller.ts

46. [x] 重构路由
    - 创建 src/modules/api/routes/model.routes.ts
    - 创建 src/modules/api/routes/memory.routes.ts
    - 创建 src/modules/api/routes/conversation.routes.ts
    - 创建 src/modules/api/routes/settings.routes.ts
    - 创建 src/modules/api/routes/tools.routes.ts
    - 创建 src/modules/api/routes/index.ts

47. [x] 创建API模块入口
    - 创建 src/modules/api/index.ts

## 第十一阶段：应用入口重构

48. [x] 创建应用模块
    - 创建 src/app.ts 用于应用初始化和配置
    - 更新 src/index.ts 使用新的模块化结构

49. [x] 创建模块索引
    - 创建 src/modules/index.ts 导出所有模块

50. [x] 清理旧文件
    - 移除所有未使用的旧服务文件
    - 移除旧的路由文件

## 第十二阶段：附加模块重构

51. [x] 重构操作模块
    - 创建 src/modules/operation 目录
    - 创建 src/modules/operation/interfaces 目录
    - 创建 src/modules/operation/interfaces/operation.interface.ts
    - 创建 src/modules/operation/operation.types.ts
    - 创建 src/modules/operation/operation.service.ts
    - 创建 src/modules/operation/operation-executor.service.ts
    - 创建 src/modules/operation/index.ts

52. [x] 重构审计模块
    - 创建 src/modules/audit 目录
    - 创建 src/modules/audit/interfaces 目录
    - 创建 src/modules/audit/interfaces/audit.interface.ts
    - 创建 src/modules/audit/audit.types.ts
    - 创建 src/modules/audit/audit.service.ts
    - 创建 src/modules/audit/index.ts

53. [x] 重构快照模块
    - 创建 src/modules/snapshot 目录
    - 创建 src/modules/snapshot/interfaces 目录
    - 创建 src/modules/snapshot/interfaces/snapshot.interface.ts
    - 创建 src/modules/snapshot/snapshot.types.ts
    - 创建 src/modules/snapshot/snapshot.service.ts
    - 创建 src/modules/snapshot/index.ts

54. [x] 重构用户模块
    - 创建 src/modules/user 目录
    - 创建 src/modules/user/interfaces 目录
    - 创建 src/modules/user/interfaces/user.interface.ts
    - 创建 src/modules/user/user-preference.service.ts
    - 创建 src/modules/user/index.ts

55. [x] 重构提示词服务
    - 创建 src/modules/model/prompt.service.ts
    - 更新 src/modules/model/index.ts

56. [x] 更新模块索引
    - 更新 src/modules/index.ts 添加新模块

57. [x] 移除旧服务文件
    - 移除 src/services 目录及其所有文件

## 第十三阶段：测试和文档

58. [x] 添加文档
    - 为每个模块添加README.md文件
    - 更新项目主README.md文件

59. [x] 清理测试文件
    - 删除旧的测试文件
    - 准备为新的模块化结构编写测试
