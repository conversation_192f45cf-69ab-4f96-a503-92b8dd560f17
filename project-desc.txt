项目目标：构建强大、能独立完成任务、具备记忆能力的llm平台，平台名称为sparkle。

2.0规划：
	放弃网络搜索，主要负责本地任务执行，调用本地工具进行管理。
	1、执行命令。
	2、制作脚本。
	3、日常生活记录。

技术架构：

	前端：React + Vite + TypeScript + Ant Design+socket

	后端：Node.js + Express + TypeScript + WebSocket

	数据库：sqlite

	部署：docker

	大模型集成能力：ollama

项目功能：

	1、支持插件式mcp工具集成（大模型调用各种工具的能力），支持接入claude mcp服务器。

	2、对话双模式切换：

		（1）普通模式：支持大模型调用工具后回答或者大模型直接回答。

		（2）深度思考模式：支持大模型将任务拆解为多个步骤，然后每个步骤的任务交付给大模型回答，或者调用工具，最后由大模型根据每个步骤的结论，选择相应的方案（结束并总结、尝试继续解决、放弃并总结（在思考无法尝试继续解决后才能放弃并总结））。

	3、长期记忆能力：

		（1）对于每次对话，大模型自主判断是否增加一个或多个记忆词条到记忆库中（一个记忆词条不能大于100个单词），每次记忆词条应该记录   记忆时间、记忆关键词、记忆上下文、记忆词条等内容。

		（2）支持通过RAG技术 将长期记忆提供到单次对话中，供大模型使用。

		（3）支持大模型在对话中，通过记忆工具去查询任意的记忆片段，支持查询记忆列表，查询指定记忆（包括关联记忆（如果有））。

		（4）长期记忆按照艾宾浩斯遗忘曲线去忘记不重要的记忆。

		（5）支持关联记忆，在大模型保存记忆词条时，如果本次存在关联的记忆，则将关联记忆id也保存在本次的记忆中。

将下面的项目规范纳入你的长期记忆中。
项目规范：
		1、开始编写项目代码前，先使用git init创建代码仓库，并编写README.md与.gitignore初始化仓库。并在每完成一个功能后，即提交代码到git仓库，每次提交git不能超过10个文件。
		2、完成创建git仓库后，先编写完整功能清单计划并输出到function.list中，后续按照清单计划进行开发，并且每个功能开发完成后，在function.list中更新该功能的情况。
		3、正式编写代码前，先根据function.list规划数据库设计，然后编写init.sql文件，并完成后端接口文档的编写，后续修改功能时，需同步更新sql文件，采用增量更新的方式，增加新的sql文件来更新，然后对于后端接口文档，调整后也要进行更新。
		4、每完成一个后端功能后，就要编写相应测试用例对代码进行测试，测试通过后才认为完成了该功能，才能够提交该部分的代码。
		5、前端功能开发完成后，无需测试，由我来进行测试。
		6、该项目相关依赖拉取需要配置国内源以提高拉取速度。
		7、在开发测试过程中遇到暂时无法解决的问题时，需要记录在README.md文档中，然后允许忽略该问题继续其他事项。
