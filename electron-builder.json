{"appId": "com.sparkle.llm", "productName": "Sparkle LLM", "copyright": "Copyright © 2023", "extraMetadata": {"homepage": "https://github.com/liuwenwu/sparkle-llm", "author": {"name": "Sparkle LLM Team", "email": "<EMAIL>"}}, "directories": {"output": "releases", "buildResources": "build"}, "files": ["dist/**/*", "node_modules/**/*", "backend/node_modules/**/*", "!node_modules/**/node_gyp_bins/**/*", "!backend/node_modules/**/node_gyp_bins/**/*", "package.json", "electron.js", "loading.html", "build/icons/**/*"], "extraResources": [{"from": ".env.production", "to": ".env.production"}, {"from": "frontend/dist", "to": "frontend/dist"}], "asar": false, "asarUnpack": ["node_modules/better-sqlite3/**/*", "node_modules/hnswlib-node/**/*", "node_modules/sharp/**/*", "node_modules/tslib/**/*", "node_modules/@xenova/**/*", "node_modules/onnxruntime-node/**/*", "node_modules/onnxruntime-web/**/*", "dist/**/*", "backend/node_modules/**/*"], "linux": {"target": ["AppImage", "deb"], "category": "Utility", "icon": "build/icons", "maintainer": "Sparkle LLM Team", "description": "Sparkle LLM Platform - A powerful LLM application with memory capabilities", "desktop": {"entry": {"Name": "Sparkle LLM", "Comment": "Sparkle LLM Platform", "Categories": "Utility;Development;AI;"}}}, "win": {"target": ["nsis", "portable"], "icon": "build/icons/icon.ico"}, "nsis": {"oneClick": false, "allowToChangeInstallationDirectory": true, "createDesktopShortcut": true, "createStartMenuShortcut": true, "shortcutName": "Sparkle LLM"}, "mac": {"target": ["dmg"], "category": "public.app-category.developer-tools", "icon": "build/icons/icon.icns"}, "dmg": {"contents": [{"x": 130, "y": 220}, {"x": 410, "y": 220, "type": "link", "path": "/Applications"}]}, "publish": null}